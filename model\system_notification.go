package model

import (
	"time"

	"github.com/Norray/xrocket/xmodel"
)

// 通知類型	接收人	觸發場景	提示內容（藍色文字表示變量）
// Jobs	Professional	有新發佈符合professional職業及可用時間的工作	A new suitable job is available for you. Please have a look.
// Jobs	Professional	Professional收到新的工作邀請	You have received a new job invitation. Please take a look and respond.
// Jobs	Professional	Professional接受工作邀請，該工作顯示在雙方的日程	The job invitation has been accepted and added to your calendar.
// Jobs	Professional	工作被facility取消	This job has been cancelled. Please take note and adjust your schedule accordingly.
// Jobs	Professional	工作在開始前1小時後或開始後4小時內取消，符合賠付條件，系統自動生成confirmation note，提醒professional前往提交	The job was cancelled, and a compensation invoice has been generated. Please review and confirm.
// Calendar	Professional	工作開始前 24 小時	You have a job in 24 hours. Please take note of the time.
// Calendar	Professional	工作開始前 2 小時	Your shift starts in 2 hours. Please be on time.
// Billing	Professional	Professional 完成工作後，要提示可以生成確認單	You have completed your shift. Please generate your confirmation note.
// Billing	Professional	facility通過confirmation note	Your confirmation note has been approved, and an invoice has been automatically generated for you.
// Billing	Professional	facility拒絕confirmation note	Your confirmation note was not approved. Please review the reason and resubmit.
// Profile	Professional	Profile資料已經審核通過，完成培訓後可以開始找工作	Your profile has been approved. Please complete the training to be eligible for jobs.
// Profile	Professional	Profile資料被駁回	Your profile was not approved. Please review the reason and make the necessary updates.
// Profile	Professional	Profile資料已經審核通過，如果ABN是sole trader，提示要去完善super資料	Your profile has been approved. Please complete your Superannuation details.
// Profile	Professional	某個文件距離日到期日不足30日	[Document Title] will expire within 30 days. Please update it promptly.
// Profile	Professional	推薦人拒絕填寫資料	Your referee [referee name] has declined to provide the required information. Please contact them or select another referee.
// Jobs	Facility	距離工作開始24小時前還未確認好人選，提示醫院要儘快確認	Only 24 hours remain until the shift starts. Please confirm the assigned professional as soon as possible.
// Jobs	Facility	距離工作開始2小時前還未有人申請工作，要提示未有人申請	Only 2 hours remain until the shift starts and no applications have been received. If no applications are received within the next hour, the job will be automatically cancelled in 1 hour.
// Jobs	Facility	距離工作開始1小時前還未有人申請工作，工作自動取消	Only 1 hour remains until the shift starts and no applications were received. The job has been automatically cancelled.
// Jobs	Facility	Professional接受工作邀請，該工作顯示在雙方的日程	The job invitation has been accepted and added to your calendar.
// Jobs	Facility	Professional拒絕工作邀請	The job invitation has been declined by [professional name]. Please contact other applicants.
// Jobs	Facility	工作邀請過期未被professional接受	The job invitation has expired without being accepted. You may contact this applicant again, or reach out to other applicants.
// Jobs	Facility	工作被Professional取消	The job has been cancelled by the professional. Please adjust your arrangements accordingly.
// Calendar	Facility	工作開始前 24 小時	Your shift starts in 24 hours. Please confirm the arrangements.
// Billing	Facility	Facility收到新的確認單，需要提醒他去審核	You have received a new confirmation note. Please review it.
// Billing	Facility	Facility收到新的發票	You have received a new invoice. Please review and process it.
// Information	Facility	資料已經審核通過，可以開始發布工作	Your information has been approved. You can now post new jobs.
// Information	Facility	資料被駁回	Your information was not approved. Please review the reason and make the necessary updates.

// 通知Demo
/*
以下是所有通知類型的示例結構值：

// ==================== Professional通知示例 ====================

// 1. Professional - 有新發佈符合職業及可用時間的工作
{
	NotificationType: SystemNotificationTypeProfessionalJobNewAvailable,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "New Job Available",
	Content:          "A new suitable job is available for you. Please have a look.",
	ActionUrl:        "/professional/jobs/456",
	ActionText:       "View Job",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobTitle": "ICU Nurse", "facilityName": "ABC Hospital", "startTime": "2025-09-15T09:00:00Z"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 2. Professional - 收到新的工作邀請
{
	NotificationType: SystemNotificationTypeProfessionalJobInvitation,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "New Job Invitation",
	Content:          "You have received a new job invitation. Please take a look and respond.",
	ActionUrl:        "/professional/job-applications/123",
	ActionText:       "View Invitation",
	RelatedId:        123,
	RelatedType:      SystemNotificationRelatedTypeJobApplication,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "jobTitle": "ICU Nurse"}`,
	CreatorUserId:    789,
	CreateTime:       time.Now(),
}

// 3. Professional - 接受工作邀請，該工作顯示在日程
{
	NotificationType: SystemNotificationTypeProfessionalJobAccepted,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Job Invitation Accepted",
	Content:          "The job invitation has been accepted and added to your calendar.",
	ActionUrl:        "/professional/calendar",
	ActionText:       "View Calendar",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "jobTitle": "ICU Nurse"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 4. Professional - 工作被facility取消
{
	NotificationType: SystemNotificationTypeProfessionalJobCancelled,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Job Cancelled",
	Content:          "This job has been cancelled. Please take note and adjust your schedule accordingly.",
	ActionUrl:        "/professional/calendar",
	ActionText:       "View Calendar",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "jobTitle": "ICU Nurse", "cancellationReason": "Staffing changes"}`,
	CreatorUserId:    789,
	CreateTime:       time.Now(),
}

// 5. Professional - 工作在開始前1小時後或開始後4小時內取消，符合賠付條件
{
	NotificationType: SystemNotificationTypeProfessionalJobCompensation,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Job Cancellation Compensation",
	Content:          "The job was cancelled, and a compensation invoice has been generated. Please review and confirm.",
	ActionUrl:        "/professional/confirmation-notes/789",
	ActionText:       "Review Confirmation",
	RelatedId:        789,
	RelatedType:      SystemNotificationRelatedTypeConfirmationNote,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "compensationAmount": 250.00}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 6. Professional - 工作開始前 24 小時
{
	NotificationType: SystemNotificationTypeProfessionalCalendar24Hour,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Upcoming Shift Reminder",
	Content:          "You have a job in 24 hours. Please take note of the time.",
	ActionUrl:        "/professional/calendar",
	ActionText:       "View Calendar",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "jobTitle": "ICU Nurse", "startTime": "2025-09-12T09:00:00Z"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 7. Professional - 工作開始前 2 小時
{
	NotificationType: SystemNotificationTypeProfessionalCalendar2Hour,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Shift Starting Soon",
	Content:          "Your shift starts in 2 hours. Please be on time.",
	ActionUrl:        "/professional/calendar",
	ActionText:       "View Calendar",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "jobTitle": "ICU Nurse", "startTime": "2025-09-11T11:00:00Z"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 8. Professional - 完成工作後，要提示可以生成確認單
{
	NotificationType: SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Generate Confirmation Note",
	Content:          "You have completed your shift. Please generate your confirmation note.",
	ActionUrl:        "/professional/confirmation-notes/create?jobId=456",
	ActionText:       "Generate Note",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "jobTitle": "ICU Nurse"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 9. Professional - facility通過confirmation note
{
	NotificationType: SystemNotificationTypeProfessionalBillingConfirmationNoteApproved,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Confirmation Note Approved",
	Content:          "Your confirmation note has been approved, and an invoice has been automatically generated for you.",
	ActionUrl:        "/professional/invoices/321",
	ActionText:       "View Invoice",
	RelatedId:        789,
	RelatedType:      SystemNotificationRelatedTypeConfirmationNote,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "invoiceId": 321, "amount": 500.00}`,
	CreatorUserId:    789,
	CreateTime:       time.Now(),
}

// 10. Professional - facility拒絕confirmation note
{
	NotificationType: SystemNotificationTypeProfessionalBillingConfirmationNoteRejected,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Confirmation Note Rejected",
	Content:          "Your confirmation note was not approved. Please review the reason and resubmit.",
	ActionUrl:        "/professional/confirmation-notes/789",
	ActionText:       "View Details",
	RelatedId:        789,
	RelatedType:      SystemNotificationRelatedTypeConfirmationNote,
	Metadata:         `{"jobId": 456, "facilityName": "ABC Hospital", "rejectionReason": "Hours do not match our records"}`,
	CreatorUserId:    789,
	CreateTime:       time.Now(),
}

// 11. Professional - Profile資料已經審核通過，完成培訓後可以開始找工作
{
	NotificationType: SystemNotificationTypeProfessionalProfileApproved,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Profile Approved",
	Content:          "Your profile has been approved. Please complete the training to be eligible for jobs.",
	ActionUrl:        "/professional/training",
	ActionText:       "Start Training",
	RelatedId:        123,
	RelatedType:      SystemNotificationRelatedTypeProfessionalProfile,
	Metadata:         `{"trainingModules": 5}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}

// 12. Professional - Profile資料被駁回
{
	NotificationType: SystemNotificationTypeProfessionalProfileRejected,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Profile Rejected",
	Content:          "Your profile was not approved. Please review the reason and make the necessary updates.",
	ActionUrl:        "/professional/profile",
	ActionText:       "Update Profile",
	RelatedId:        123,
	RelatedType:      SystemNotificationRelatedTypeProfessionalProfile,
	Metadata:         `{"rejectionReason": "Missing required qualifications documentation"}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}

// 13. Professional - Profile資料已經審核通過，如果ABN是sole trader，提示要去完善super資料
{
	NotificationType: SystemNotificationTypeProfessionalProfileNeedSuper,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Complete Superannuation Details",
	Content:          "Your profile has been approved. Please complete your Superannuation details.",
	ActionUrl:        "/professional/profile/superannuation",
	ActionText:       "Update Details",
	RelatedId:        123,
	RelatedType:      SystemNotificationRelatedTypeProfessionalSuperannuation,
	Metadata:         `{"abnType": "Sole Trader"}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}

// 14. Professional - 某個文件距離日到期日不足30日
{
	NotificationType: SystemNotificationTypeProfessionalProfileDocumentExpire,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Document Expiring Soon",
	Content:          "Medical Registration will expire within 30 days. Please update it promptly.",
	ActionUrl:        "/professional/profile/documents",
	ActionText:       "Update Document",
	RelatedId:        321,
	RelatedType:      SystemNotificationRelatedTypeProfessionalFile,
	Metadata:         `{"documentType": "Medical Registration", "expiryDate": "2025-10-15"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 15. Professional - 推薦人拒絕填寫資料
{
	NotificationType: SystemNotificationTypeProfessionalProfileReferenceDeclined,
	TargetType:       SystemNotificationTargetTypeProfessional,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Reference Declined",
	Content:          "Your referee Dr. Johnson has declined to provide the required information. Please contact them or select another referee.",
	ActionUrl:        "/professional/profile/references",
	ActionText:       "Update References",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeProfessionalReference,
	Metadata:         `{"refereeName": "Dr. Johnson", "refereeEmail": "<EMAIL>"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// ==================== Facility通知示例 ====================

// 16. Facility - 距離工作開始24小時前還未確認好人選，提示醫院要儘快確認
{
	NotificationType: SystemNotificationTypeFacilityJobNeedConfirm,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Confirm Professional Assignment",
	Content:          "Only 24 hours remain until the shift starts. Please confirm the assigned professional as soon as possible.",
	ActionUrl:        "/facility/jobs/456/applications",
	ActionText:       "Review Applications",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobTitle": "ICU Nurse", "startTime": "2025-09-12T09:00:00Z", "applicationsCount": 3}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 17. Facility - 距離工作開始2小時前還未有人申請工作，要提示未有人申請
{
	NotificationType: SystemNotificationTypeFacilityJobNoApplication,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityUrgent,
	Title:            "No Applications Received",
	Content:          "Only 2 hours remain until the shift starts and no applications have been received. If no applications are received within the next hour, the job will be automatically cancelled in 1 hour.",
	ActionUrl:        "/facility/jobs/456",
	ActionText:       "View Job",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobTitle": "ICU Nurse", "startTime": "2025-09-11T11:00:00Z"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 18. Facility - 距離工作開始1小時前還未有人申請工作，工作自動取消
{
	NotificationType: SystemNotificationTypeFacilityJobAutoCancel,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Job Automatically Cancelled",
	Content:          "Only 1 hour remains until the shift starts and no applications were received. The job has been automatically cancelled.",
	ActionUrl:        "/facility/jobs/456",
	ActionText:       "View Details",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobTitle": "ICU Nurse", "startTime": "2025-09-11T11:00:00Z"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 19. Facility - Professional接受工作邀請，該工作顯示在雙方的日程
{
	NotificationType: SystemNotificationTypeFacilityJobAccepted,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Job Invitation Accepted",
	Content:          "The job invitation has been accepted and added to your calendar.",
	ActionUrl:        "/facility/calendar",
	ActionText:       "View Calendar",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "jobTitle": "ICU Nurse"}`,
	CreatorUserId:    123,
	CreateTime:       time.Now(),
}

// 20. Facility - Professional拒絕工作邀請
{
	NotificationType: SystemNotificationTypeFacilityJobDecline,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Job Invitation Declined",
	Content:          "The job invitation has been declined by Dr. Smith. Please contact other applicants.",
	ActionUrl:        "/facility/jobs/456/applications",
	ActionText:       "View Applications",
	RelatedId:        123,
	RelatedType:      SystemNotificationRelatedTypeJobApplication,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "jobTitle": "ICU Nurse", "declineReason": "Schedule conflict"}`,
	CreatorUserId:    123,
	CreateTime:       time.Now(),
}

// 21. Facility - 工作邀請過期未被professional接受
{
	NotificationType: SystemNotificationTypeFacilityJobInvitationExpired,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Job Invitation Expired",
	Content:          "The job invitation has expired without being accepted. You may contact this applicant again, or reach out to other applicants.",
	ActionUrl:        "/facility/jobs/456/applications",
	ActionText:       "View Applications",
	RelatedId:        123,
	RelatedType:      SystemNotificationRelatedTypeJobApplication,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "jobTitle": "ICU Nurse"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 22. Facility - 工作被Professional取消
{
	NotificationType: SystemNotificationTypeFacilityJobCancelled,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Job Cancelled by Professional",
	Content:          "The job has been cancelled by the professional. Please adjust your arrangements accordingly.",
	ActionUrl:        "/facility/jobs/456",
	ActionText:       "View Details",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "jobTitle": "ICU Nurse", "cancellationReason": "Medical emergency"}`,
	CreatorUserId:    123,
	CreateTime:       time.Now(),
}

// 23. Facility - 工作開始前 24 小時
{
	NotificationType: SystemNotificationTypeFacilityCalendar24Hour,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Upcoming Shift Reminder",
	Content:          "Your shift starts in 24 hours. Please confirm the arrangements.",
	ActionUrl:        "/facility/calendar",
	ActionText:       "View Calendar",
	RelatedId:        456,
	RelatedType:      SystemNotificationRelatedTypeJob,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "jobTitle": "ICU Nurse", "startTime": "2025-09-12T09:00:00Z"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 24. Facility - 收到新的確認單，需要提醒他去審核
{
	NotificationType: SystemNotificationTypeFacilityBillingConfirmationNoteReceived,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "New Confirmation Note",
	Content:          "You have received a new confirmation note. Please review it.",
	ActionUrl:        "/facility/confirmation-notes/789",
	ActionText:       "Review Note",
	RelatedId:        789,
	RelatedType:      SystemNotificationRelatedTypeConfirmationNote,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "amount": 500.00}`,
	CreatorUserId:    123,
	CreateTime:       time.Now(),
}

// 25. Facility - 收到新的發票
{
	NotificationType: SystemNotificationTypeFacilityBillingInvoiceReceived,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "New Invoice",
	Content:          "You have received a new invoice. Please review and process it.",
	ActionUrl:        "/facility/invoices/321",
	ActionText:       "View Invoice",
	RelatedId:        321,
	RelatedType:      SystemNotificationRelatedTypeInvoice,
	Metadata:         `{"jobId": 456, "professionalName": "Dr. Smith", "amount": 500.00, "dueDate": "2025-10-11"}`,
	CreatorUserId:    0,
	CreateTime:       time.Now(),
}

// 26. Facility - 資料已經審核通過，可以開始發布工作
{
	NotificationType: SystemNotificationTypeFacilityInformationApproved,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "Information Approved",
	Content:          "Your information has been approved. You can now post new jobs.",
	ActionUrl:        "/facility/jobs/create",
	ActionText:       "Post Job",
	RelatedId:        789,
	RelatedType:      SystemNotificationRelatedTypeFacilityProfile,
	Metadata:         `{}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}

// 27. Facility - 資料被駁回
{
	NotificationType: SystemNotificationTypeFacilityInformationRejected,
	TargetType:       SystemNotificationTargetTypeFacility,
	Priority:         SystemNotificationPriorityHigh,
	Title:            "Information Rejected",
	Content:          "Your information was not approved. Please review the reason and make the necessary updates.",
	ActionUrl:        "/facility/profile",
	ActionText:       "Update Profile",
	RelatedId:        789,
	RelatedType:      SystemNotificationRelatedTypeFacilityProfile,
	Metadata:         `{"rejectionReason": "Missing business registration documents"}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}

// ==================== 系統公告示例 ====================

// 28. 系統公告
{
	NotificationType: SystemNotificationTypeSystemAnnouncement,
	TargetType:       SystemNotificationTargetTypeAll,
	Priority:         SystemNotificationPriorityNormal,
	Title:            "New Feature Announcement",
	Content:          "We've added new features to improve your experience. Check out the updated dashboard!",
	ActionUrl:        "/dashboard",
	ActionText:       "View Dashboard",
	RelatedId:        0,
	RelatedType:      "",
	Metadata:         `{"featureList": ["Improved calendar view", "Enhanced notification settings", "New payment options"]}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}

// 29. 維護通知
{
	NotificationType: SystemNotificationTypeMaintenanceNotice,
	TargetType:       SystemNotificationTargetTypeAll,
	Priority:         SystemNotificationPriorityUrgent,
	Title:            "Scheduled Maintenance",
	Content:          "The system will be unavailable for maintenance on September 15, 2025, from 02:00 to 04:00 UTC.",
	ActionUrl:        "",
	ActionText:       "",
	RelatedId:        0,
	RelatedType:      "",
	Metadata:         `{"startTime": "2025-09-15T02:00:00Z", "endTime": "2025-09-15T04:00:00Z"}`,
	CreatorUserId:    1,
	CreateTime:       time.Now(),
}
*/

// 通知類型常量
const (
	// Professional - 工作相關通知
	SystemNotificationTypeProfessionalJobInvitation          = "PROFESSIONAL_JOB_INVITATION"            // 工作邀請
	SystemNotificationTypeProfessionalJobAccepted            = "PROFESSIONAL_JOB_ACCEPTED"              // 工作接受
	SystemNotificationTypeProfessionalJobCancelled           = "PROFESSIONAL_JOB_CANCELLED"             // 工作取消
	SystemNotificationTypeProfessionalJobCompensation        = "PROFESSIONAL_JOB_COMPENSATION"          // 工作賠付
	SystemNotificationTypeProfessionalJobNewAvailable        = "PROFESSIONAL_JOB_NEW_AVAILABLE"         // 新工作可用
	SystemNotificationTypeProfessionalJobOrientationDocument = "PROFESSIONAL_JOB_ORIENTATION_DOCUMENT " // 閱讀工作指導文件

	// Professional - 日程相關通知
	SystemNotificationTypeProfessionalCalendar24Hour = "PROFESSIONAL_CALENDAR_24_HOUR" // 24小時提醒
	SystemNotificationTypeProfessionalCalendar2Hour  = "PROFESSIONAL_CALENDAR_2_HOUR"  // 2小時提醒

	// Professional - 賬單相關通知
	SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate = "PROFESSIONAL_BILLING_CONFIRMATION_NOTE_GENERATE" // 生成確認單
	SystemNotificationTypeProfessionalBillingConfirmationNoteApproved = "PROFESSIONAL_BILLING_CONFIRMATION_NOTE_APPROVED" // 確認單通過
	SystemNotificationTypeProfessionalBillingConfirmationNoteRejected = "PROFESSIONAL_BILLING_CONFIRMATION_NOTE_REJECTED" // 確認單拒絕

	// Professional - 個人資料相關通知
	SystemNotificationTypeProfessionalProfileApproved          = "PROFESSIONAL_PROFILE_APPROVED"           // 資料通過
	SystemNotificationTypeProfessionalProfileRejected          = "PROFESSIONAL_PROFILE_REJECTED"           // 資料駁回
	SystemNotificationTypeProfessionalProfileNeedSuper         = "PROFESSIONAL_PROFILE_NEED_SUPER"         // 需要完善super資料
	SystemNotificationTypeProfessionalProfileDocumentExpire    = "PROFESSIONAL_PROFILE_DOCUMENT_EXPIRE"    // 文件即將到期
	SystemNotificationTypeProfessionalProfileReferenceDeclined = "PROFESSIONAL_PROFILE_REFERENCE_DECLINED" // 推薦人拒絕

	// Facility - 工作相關通知
	SystemNotificationTypeFacilityJobAccepted          = "FACILITY_JOB_ACCEPTED"           // 工作接受
	SystemNotificationTypeFacilityJobCancelled         = "FACILITY_JOB_CANCELLED"          // 工作取消
	SystemNotificationTypeFacilityJobDecline           = "FACILITY_JOB_DECLINE"            // 工作拒絕
	SystemNotificationTypeFacilityJobInvitationExpired = "FACILITY_JOB_INVITATION_EXPIRED" // 工作邀請過期
	SystemNotificationTypeFacilityJobNoApplication     = "FACILITY_JOB_NO_APPLICATION"     // 工作無申請
	SystemNotificationTypeFacilityJobAutoCancel        = "FACILITY_JOB_AUTO_CANCEL"        // 工作自動取消
	SystemNotificationTypeFacilityJobNeedConfirm       = "FACILITY_JOB_NEED_CONFIRM"       // 工作需要確認

	// Facility - 日程相關通知
	SystemNotificationTypeFacilityCalendar24Hour = "FACILITY_CALENDAR_24_HOUR" // 24小時提醒

	// Facility - 賬單相關通知
	SystemNotificationTypeFacilityBillingConfirmationNoteReceived = "FACILITY_BILLING_CONFIRMATION_NOTE_RECEIVED" // 收到確認單
	SystemNotificationTypeFacilityBillingInvoiceReceived          = "FACILITY_BILLING_INVOICE_RECEIVED"           // 收到發票

	// Facility - 機構信息相關通知
	SystemNotificationTypeFacilityInformationApproved = "FACILITY_INFORMATION_APPROVED" // 機構信息通過
	SystemNotificationTypeFacilityInformationRejected = "FACILITY_INFORMATION_REJECTED" // 機構信息駁回

	// 系統公告類型 (適用於所有用戶)
	SystemNotificationTypeSystemAnnouncement = "SYSTEM_ANNOUNCEMENT" // 系統公告
	SystemNotificationTypeMaintenanceNotice  = "MAINTENANCE_NOTICE"  // 維護通知
)

// 關聯業務類型常量 (RelatedType)
const (
	// 工作相關
	SystemNotificationRelatedTypeJob            = "JOB"             // 工作
	SystemNotificationRelatedTypeJobApplication = "JOB_APPLICATION" // 工作申請
	SystemNotificationRelatedTypeJobShift       = "JOB_SHIFT"       // 工作班次

	// 賬單相關
	SystemNotificationRelatedTypeConfirmationNote = "CONFIRMATION_NOTE" // 確認單
	SystemNotificationRelatedTypeInvoice          = "INVOICE"           // 發票

	// 個人資料相關
	SystemNotificationRelatedTypeProfessionalProfile              = "PROFESSIONAL_PROFILE"        // 個人資料
	SystemNotificationRelatedTypeProfessionalFile                 = "PROFESSIONAL_FILE"           // 文件
	SystemNotificationRelatedTypeProfessionalReference            = "PROFESSIONAL_REFERENCE"      // 推薦人
	SystemNotificationRelatedTypeProfessionalSuperannuation       = "PROFESSIONAL_SUPERANNUATION" // 養老金
	SystemNotificationRelatedTypeProfessionalProfessionalTraining = "PROFESSIONAL_TRAINING"       // 專業培訓

	// 機構信息相關
	SystemNotificationRelatedTypeFacility        = "FACILITY"         // 機構
	SystemNotificationRelatedTypeFacilityFile    = "FACILITY_FILE"    // 機構文件
	SystemNotificationRelatedTypeFacilityProfile = "FACILITY_PROFILE" // 機構資料
)

// 通知目標類型常量
const (
	SystemNotificationTargetTypeProfessional = "PROFESSIONAL" // 專業人士
	SystemNotificationTargetTypeFacility     = "FACILITY"     // 機構
	SystemNotificationTargetTypeAll          = "ALL"          // 所有用戶
	SystemNotificationTargetTypeSystemAdmin  = "SYSTEM_ADMIN" // 系統管理員
)

// 通知優先級常量
const (
	SystemNotificationPriorityLow    = "LOW"    // 低優先級
	SystemNotificationPriorityNormal = "NORMAL" // 普通優先級
	SystemNotificationPriorityHigh   = "HIGH"   // 高優先級
	SystemNotificationPriorityUrgent = "URGENT" // 緊急
)

// 系統通知
type SystemNotification struct {
	Id               uint64    `json:"id" gorm:"primary_key"`
	NotificationType string    `json:"notificationType" gorm:"type:varchar(64);index:notification_type_idx;not null"` // 通知類型
	TargetType       string    `json:"targetType" gorm:"type:varchar(32);index:target_type_idx;not null"`             // 目標類型: PROFESSIONAL, FACILITY, ALL, SYSTEM_ADMIN
	Priority         string    `json:"priority" gorm:"type:varchar(16);default:'NORMAL';not null"`                    // 優先級: LOW, NORMAL, HIGH, URGENT
	Title            string    `json:"title" gorm:"type:varchar(512);not null"`                                       // 通知標題
	Content          string    `json:"content" gorm:"type:text;not null"`                                             // 通知內容
	ActionUrl        string    `json:"actionUrl" gorm:"type:varchar(512)"`                                            // 操作連結
	ActionText       string    `json:"actionText" gorm:"type:varchar(128)"`                                           // 操作按鈕文字
	RelatedId        uint64    `json:"relatedId" gorm:"index:related_id_idx"`                                         // 關聯業務ID（如工作ID、申請ID等）
	RelatedType      string    `json:"relatedType" gorm:"type:varchar(64);index:related_type_idx"`                    // 關聯業務類型（如 JOB, APPLICATION, PROFILE等）
	Metadata         string    `json:"metadata" gorm:"type:text"`                                                     // 額外元數據JSON
	CreatorUserId    uint64    `json:"creatorUserId" gorm:"index:creator_user_idx"`                                   // 創建用戶ID (0表示系統生成)
	CreateTime       time.Time `json:"createTime" gorm:"type:datetime(0);not null"`                                   // 創建時間
	xmodel.Model
}

func (m SystemNotification) TableName() string {
	return "system_notification"
}

func (m SystemNotification) SwaggerDescription() string {
	return "系統通知"
}
