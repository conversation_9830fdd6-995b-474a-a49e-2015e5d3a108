package job

import (
	"context"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const (
	CronCheckJobReminders                    = "cron_check_job_reminders" // 檢查工作提醒
	CheckJobRemindersMaxProcessRecordsPerRun = 100                        // 每次處理的最大記錄數
	CheckJobRemindersLockTimeoutSeconds      = 50                         // 鎖定超時時間（秒）
)

type FacilityJobConfirmationInfo struct {
	JobId      uint64
	FacilityId uint64
	UserId     uint64
	BeginTime  time.Time
}

type JobCompletionInfo struct {
	JobId              uint64
	JobApplicationId   uint64
	ProfessionalUserId uint64
	FacilityId         uint64
	EndTime            time.Time
}

// 檢查工作提醒定時任務 - 每分鐘執行
func jobCheckJobReminders() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronCheckJobReminders)
	if err != nil {
		logger.Errorf("[CRON] fail to check job reminders task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronCheckJobReminders)
		return
	}

	nowTime := time.Now().UTC().Truncate(time.Second)

	// 檢查24小時提醒 - Professional和Facility
	check24HourReminders(db, nowTime, logger)

	// 檢查2小時提醒 - Professional
	check2HourReminders(db, nowTime, logger)

	// 檢查工作開始前2小時無人申請 - Facility
	checkNoApplication2Hour(db, nowTime, logger)

	// 檢查工作開始前1小時無人申請自動取消 - Facility
	checkAutoCancel1Hour(db, nowTime, logger)

	// 檢查距離工作開始24小時前還未確認好人選的工作
	checkUnconfirmedJobs24Hours(db, nowTime, logger)

	// 檢查工作完成後的確認單生成提醒
	checkJobCompletionConfirmationNote(db, nowTime, logger)

	logger.Info("job reminders check completed")
}

// 檢查24小時提醒
func check24HourReminders(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 查詢24小時後開始的工作
	var jobReminders []services.JobReminderInfo

	// Professional 24小時提醒 - 查詢已接受的工作申請（排除已發送通知的）
	builder := db.Table("job AS j").
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalCalendar24Hour, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = ja.user_id AND snu.deleted = ?",
			model.SystemNotificationUserDeletedN).
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"j.facility_id",
			"j.position_profession AS job_title",
			"j.begin_time",
			"ja.user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeProfessional24Hour),
		}).
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(24*time.Hour)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get 24 hour professional reminders: %v", err)
		return
	}

	// 在事務中批量創建專業人員24小時提醒通知
	if len(jobReminders) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, jobReminders)
		})
		if err != nil {
			logger.Errorf("[CRON] fail to send 24 hour reminders to professionals: %v", err)
		} else {
			logger.Infof("Successfully sent 24 hour reminders to %d professionals", len(jobReminders))
		}
	}

	// Facility 24小時提醒 - 查詢有已接受申請的工作（排除已發送通知的）
	var facilityReminders []services.JobReminderInfo
	facilityBuilder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id", // 記錄已接受的申請ID
			"j.facility_id",
			"j.position_profession AS job_title",
			"j.begin_time",
			"fu.user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeFacility24Hour),
		}).
		Joins("JOIN facility_user AS fu ON fu.facility_id = j.facility_id AND fu.primary_user = 'Y'").
		Joins("JOIN job_application AS ja ON ja.job_id = j.id AND ja.status = ? AND ja.accept = ?", model.JobApplicationStatusAccept, model.JobApplicationAcceptY).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeFacilityCalendar24Hour, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = fu.user_id AND snu.deleted = ?",
			model.SystemNotificationUserDeletedN).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(24*time.Hour)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Group("j.id, j.facility_id, fu.user_id, j.begin_time").
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := facilityBuilder.Scan(&facilityReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get 24 hour facility reminders: %v", err)
		return
	}

	// 在事務中批量創建機構24小時提醒通知
	if len(facilityReminders) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityCalendar24HourBatch(tx, facilityReminders)
		})
		if err != nil {
			logger.Errorf("[CRON] fail to send 24 hour reminders to facilities: %v", err)
		} else {
			logger.Infof("Successfully sent 24 hour reminders to %d facilities", len(facilityReminders))
		}
	}
}

// 檢查2小時提醒 - Professional
func check2HourReminders(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"j.facility_id",
			"j.position_profession AS job_title",
			"j.begin_time",
			"ja.user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeProfessional2Hour),
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalCalendar2Hour, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = ja.user_id AND snu.deleted = ?",
			model.SystemNotificationUserDeletedN).
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(2*time.Hour)).
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get 2 hour professional reminders: %v", err)
		return
	}

	// 在事務中批量創建專業人員2小時提醒通知
	if len(jobReminders) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar2HourBatch(tx, jobReminders)
		})
		if err != nil {
			logger.Errorf("[CRON] fail to send 2 hour reminders to professionals: %v", err)
		} else {
			logger.Infof("Successfully sent 2 hour reminders to %d professionals", len(jobReminders))
		}
	}
}

// 檢查工作開始前2小時無人申請
func checkNoApplication2Hour(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"0 AS job_application_id", // 無申請提醒不需要具體的申請ID
			"j.facility_id",
			"j.position_profession AS job_title",
			"j.begin_time",
			"fu.user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeNoApplication2Hour),
		}).
		Joins("JOIN facility_user AS fu ON fu.facility_id = j.facility_id AND fu.primary_user = 'Y'").
		Joins("LEFT JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeFacilityJobNoApplication, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = fu.user_id AND snu.deleted = ?",
			model.SystemNotificationUserDeletedN).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(2*time.Hour)).
		Where("ja.id IS NULL").  // 無申請記錄
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Group("j.id, j.facility_id, fu.user_id, j.begin_time").
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get no application 2 hour reminders: %v", err)
		return
	}

	// 在事務中批量創建機構無申請2小時提醒通知
	if len(jobReminders) > 0 {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityJobNoApplicationBatch(tx, jobReminders)
		})
		if err != nil {
			logger.Errorf("[CRON] fail to send no application reminders to facilities: %v", err)
		} else {
			logger.Infof("Successfully sent no application reminders to %d facilities", len(jobReminders))
		}
	}
}

// 檢查工作開始前1小時無人申請自動取消
func checkAutoCancel1Hour(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"0 AS job_application_id", // 自動取消提醒不需要具體的申請ID
			"j.facility_id",
			"j.position_profession AS job_title",
			"j.begin_time",
			"fu.user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeAutoCancel1Hour),
		}).
		Joins("JOIN facility_user AS fu ON fu.facility_id = j.facility_id AND fu.primary_user = 'Y'").
		Joins("LEFT JOIN job_application AS ja ON ja.job_id = j.id").
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(1*time.Hour)).
		Where("ja.id IS NULL"). // 無申請記錄
		Group("j.id, j.facility_id, fu.user_id, j.begin_time").
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get auto cancel 1 hour reminders: %v", err)
		return
	}

	// 設置事務超時
	ctx, cancel := context.WithTimeout(context.Background(), CheckJobRemindersLockTimeoutSeconds*time.Second)
	defer cancel()

	var cancelledJobs []services.JobReminderInfo

	// 在單一事務中處理所有工作取消和通知發送
	err := db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, reminder := range jobReminders {
			// 自動取消工作
			result := tx.Model(&model.Job{}).
				Where("id = ?", reminder.JobId).
				Where("status = ?", model.JobStatusPublish).
				Updates(map[string]interface{}{
					"status":        model.JobStatusCancel,
					"cancel_reason": "No applications received within 1 hour of start time",
					"update_time":   nowTime,
				})

			if result.Error != nil {
				logger.Errorf("[CRON] fail to auto cancel job %d: %v", reminder.JobId, result.Error)
				continue // 繼續處理其他工作，不回滾整個事務
			}

			if result.RowsAffected > 0 {
				// 收集成功取消的工作
				cancelledJobs = append(cancelledJobs, reminder)
			}
		}

		// 在同一事務中批量創建自動取消通知
		if len(cancelledJobs) > 0 {
			if err := services.SystemNotificationService.CreateFacilityJobAutoCancelBatch(tx, cancelledJobs); err != nil {
				logger.Errorf("[CRON] fail to send auto cancel notifications to facilities: %v", err)
				return err // 如果通知發送失敗，回滾整個事務
			}
		}

		return nil
	})

	if err != nil {
		logger.Errorf("[CRON] transaction failed for auto cancel jobs: %v", err)
	} else if len(cancelledJobs) > 0 {
		logger.Infof("Successfully auto cancelled %d jobs and sent notifications", len(cancelledJobs))
	}
}

// 檢查距離工作開始24小時前還未確認好人選的工作
func checkUnconfirmedJobs24Hours(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// TODO: 距離工作開始24小時前還未確認好人選，提示醫院要儘快確認 - 通知Facility

	var unconfirmedJobs []FacilityJobConfirmationInfo

	// 查詢24小時後開始但還未確認人選的工作
	// 條件：有申請但沒有被接受的申請
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"j.facility_id",
			"fu.user_id",
			"j.begin_time",
		}).
		Joins("JOIN facility_user AS fu ON fu.facility_id = j.facility_id AND fu.primary_user = 'Y'").
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN job_application AS ja_accepted ON ja_accepted.job_id = j.id AND ja_accepted.status = ? AND ja_accepted.accept = ?",
			model.JobApplicationStatusAccept, model.JobApplicationAcceptY).
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(24*time.Hour)).
		Where("ja_accepted.id IS NULL"). // 沒有被接受的申請
		Group("j.id, j.facility_id, fu.user_id, j.begin_time").
		Having("COUNT(DISTINCT ja.id) > 0"). // 但有申請記錄
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&unconfirmedJobs).Error; err != nil {
		logger.Errorf("[CRON] fail to get unconfirmed jobs 24 hours: %v", err)
		return
	}

	for _, job := range unconfirmedJobs {
		// TODO: 調用通知服務發送需要確認人選提醒給Facility
		logger.Infof("Should send job confirmation reminder to facility - JobId: %d, FacilityId: %d", job.JobId, job.FacilityId)
	}
}

// 檢查工作完成後的確認單生成提醒
func checkJobCompletionConfirmationNote(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 工作完成後4小時，提醒Professional生成確認單

	var completedJobs []JobCompletionInfo

	// 查詢4小時前結束的工作，且專業人士已接受但還未生成確認單（排除已發送通知的）
	fourHoursAgo := nowTime.Add(-4 * time.Hour)
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"ja.id AS job_application_id",
			"ja.user_id AS professional_user_id",
			"j.facility_id",
			"j.end_time",
		}).
		Joins("JOIN job_application AS ja ON ja.job_id = j.id").
		Joins("LEFT JOIN document AS d ON d.job_application_id = ja.id AND d.category = ?", model.DocumentCategoryConfirmation).
		Joins("LEFT JOIN system_notification sn ON sn.related_id = j.id AND sn.notification_type = ? AND sn.related_type = ?",
			model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate, model.SystemNotificationRelatedTypeJob).
		Joins("LEFT JOIN system_notification_user snu ON sn.id = snu.system_notification_id AND snu.user_id = ja.user_id AND snu.deleted = ?",
			model.SystemNotificationUserDeletedN).
		Where("j.status = ?", model.JobStatusPublish).
		Where("ja.status = ?", model.JobApplicationStatusAccept).
		Where("ja.accept = ?", model.JobApplicationAcceptY).
		Where("j.begin_time < ?", nowTime).
		Where("j.end_time BETWEEN ? AND ?", fourHoursAgo.Add(-10*time.Minute), fourHoursAgo.Add(10*time.Minute)).
		Where("d.id IS NULL").   // 還未生成確認單
		Where("snu.id IS NULL"). // 排除已經發送過通知的
		Limit(CheckJobRemindersMaxProcessRecordsPerRun)

	if err := builder.Scan(&completedJobs).Error; err != nil {
		logger.WithError(err).Error("Failed to get completed jobs for confirmation note reminder")
		return
	}

	if len(completedJobs) == 0 {
		logger.Debug("No completed jobs found for confirmation note reminder")
		return
	}

	// 轉換為JobReminderInfo格式以使用批量通知服務
	var jobReminders []services.JobReminderInfo
	for _, job := range completedJobs {
		jobReminders = append(jobReminders, services.JobReminderInfo{
			JobId:            job.JobId,
			JobApplicationId: job.JobApplicationId,
			FacilityId:       job.FacilityId,
			JobTitle:         "",          // 這裡可以從job表獲取，但對於確認單提醒不是必需的
			BeginTime:        job.EndTime, // 使用結束時間
			UserId:           job.ProfessionalUserId,
			ReminderType:     services.ReminderTypeJobCompletionProfessional,
		})
	}

	logger.WithField("jobCount", len(jobReminders)).Info("Processing confirmation note reminders")

	// 使用事務處理批量通知
	err := db.Transaction(func(tx *gorm.DB) error {
		return services.SystemNotificationService.CreateProfessionalBillingConfirmationNoteGenerateBatch(tx, jobReminders)
	})

	if err != nil {
		logger.WithError(err).Error("Failed to send confirmation note reminders to professionals")
	} else {
		logger.WithField("successCount", len(jobReminders)).Info("Successfully sent confirmation note reminders to professionals")
	}
}
