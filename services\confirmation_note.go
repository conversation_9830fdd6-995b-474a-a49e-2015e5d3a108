package services

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/dustin/go-humanize"

	"github.com/jinzhu/now"

	"github.com/jinzhu/copier"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xtool"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

var documentItemTypeStringMap = map[string]string{
	model.DocumentItemTypeWages:               "Wages",
	model.DocumentItemTypeAdditionalWages:     "Overtime Allowance",
	model.DocumentItemTypeTravelReimbursement: "Travel Reimbursement",
	model.DocumentItemTypeMeals:               "Meal Reimbursement",
	model.DocumentItemTypeAccommodation:       "Accommodation Reimbursement",
	model.DocumentItemTypeSuperAmount:         "Superannuation",
	model.DocumentItemTypeCommissionAmount:    "Commission",
	model.DocumentItemTypeCancellationFee:     "Cancellation Fee",
}

var ConfirmationNoteService = new(confirmationNoteService)

type confirmationNoteService struct{}

// region ---------------------------------------------------- 檢查函數 ----------------------------------------------------

func (s *confirmationNoteService) CheckIdExist(db *gorm.DB, m *model.Document, category string, id uint64, userId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.confirmation_note.id.does_not_exist",
		Other: "No such record, please try after reloading.",
	}
	var err error
	builder := db.Where("category = ?", category)
	if len(userId) > 0 {
		builder = builder.Where("user_id = ?", userId[0])
	}
	if err = builder.First(&m, id).Error; xgorm.IsSqlErr(err) {
		return false, msg, err
	}
	if xgorm.IsNotFoundErr(err) {
		return false, msg, nil
	} else {
		return true, msg, nil
	}
}

// 檢查確認通知單是否可以修改（僅DRAFT狀態可修改）
func (s *confirmationNoteService) CheckCanModify(db *gorm.DB, category string, id uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.confirmation_note.cannot_modify",
		Other: "This confirmation note cannot be modified in current status.",
	}

	var document model.Document
	var err error
	if err = db.
		Where("category = ?", category).
		Where("user_id = ?", userId).
		First(&document, id).Error; err != nil {
		if xgorm.IsNotFoundErr(err) {
			return false, i18n.Message{
				ID:    "checker.confirmation_note.id.does_not_exist",
				Other: "No such record, please try after reloading.",
			}, nil
		}
		return false, msg, err
	}

	// 只有DRAFT狀態可以修改
	if document.Progress != model.DocumentProgressDraft {
		return false, msg, nil
	}

	return true, msg, nil
}

// 檢查確認通知單是否可以提交（僅DRAFT狀態可提交）
func (s *confirmationNoteService) CheckCanSubmit(db *gorm.DB, category string, id uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.confirmation_note.cannot_submit",
		Other: "This confirmation note cannot be submitted in current status.",
	}

	var document model.Document
	var err error
	if err = db.
		Where("category = ?", category).
		Where("user_id = ?", userId).
		First(&document, id).Error; err != nil {
		if xgorm.IsNotFoundErr(err) {
			return false, i18n.Message{
				ID:    "checker.confirmation_note.id.does_not_exist",
				Other: "No such record, please try after reloading.",
			}, nil
		}
		return false, msg, err
	}

	// DRAFT 和 REJECT 狀態可以提交
	if document.Progress != model.DocumentProgressDraft && document.Progress != model.DocumentProgressReject {
		return false, msg, nil
	}

	// 檢查是否為用戶的數據
	if document.UserId != userId {
		return false, i18n.Message{
			ID:    "checker.confirmation_note.not_creator",
			Other: "You are not the creator of this confirmation note.",
		}, nil
	}

	return true, msg, nil
}

// 檢查確認通知單是否可以取消（僅DRAFT和SENT狀態可作廢）
func (s *confirmationNoteService) CheckCanCancel(db *gorm.DB, category string, id uint64, userId uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.confirmation_note.cannot_void",
		Other: "This confirmation note cannot be voided in current status.",
	}

	var document model.Document
	var err error
	if err = db.
		Where("category = ?", category).
		Where("user_id = ?", userId).
		First(&document, id).Error; err != nil {
		if xgorm.IsNotFoundErr(err) {
			return false, i18n.Message{
				ID:    "checker.confirmation_note.id.does_not_exist",
				Other: "No such record, please try after reloading.",
			}, nil
		}
		return false, msg, err
	}

	// 只有DRAFT和SENT狀態可以作廢
	if document.Progress != model.DocumentProgressDraft &&
		document.Progress != model.DocumentProgressSent {
		return false, msg, nil
	}

	return true, msg, nil
}

// 檢查確認通知單是否可以審核/拒絕（僅SENT狀態可審核/拒絕）
func (s *confirmationNoteService) CheckCanReview(db *gorm.DB, id uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.confirmation_note.cannot_review",
		Other: "This confirmation note cannot be reviewed in current status.",
	}

	var document model.Document
	var err error
	if err = db.Where("category = ?", model.DocumentCategoryConfirmation).First(&document, id).Error; err != nil {
		return false, msg, err
	}

	// 只有SENT狀態可以審核/拒絕
	if document.Progress != model.DocumentProgressSent {
		return false, msg, nil
	}

	return true, msg, nil
}

// endregion ---------------------------------------------------- 檢查函數 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士列表 ----------------------------------------------------

type ProfessionalConfirmationNoteListReq struct {
	DocumentNo string `form:"documentNo"`                                                                 // 確認通知單號碼
	Progress   string `form:"progress" binding:"omitempty,splitin=DRAFT SENT REJECT CONFIRM PAID CANCEL"` // 狀態 DRAFT SENT REJECT CONFIRM PAID CANCEL
	StartDate  string `form:"startDate" binding:"omitempty,datetime=2006-01-02"`                          // 開始日期
	EndDate    string `form:"endDate" binding:"omitempty,datetime=2006-01-02"`                            // 結束日期
	Particular string `form:"particular"`                                                                 // 描述
	ToName     string `form:"toName"`                                                                     // 付款人
	JobId      uint64 `form:"jobId"`                                                                      // 工作ID
}

type ProfessionalConfirmationNoteListResp struct {
	DocumentId       uint64          `json:"documentId"`
	DocumentNo       string          `json:"documentNo"`
	FromName         string          `json:"fromName"` // 發件人姓名
	ToName           string          `json:"toName"`
	DocumentDate     xtype.Date      `swaggertype:"string" json:"documentDate"`
	Particular       string          `json:"particular"`
	Progress         string          `json:"progress"`
	GrandTotal       decimal.Decimal `json:"grandTotal"`
	RelatedInvoiceId string          `json:"relatedInvoiceId"`
	CanEdit          string          `json:"canEdit"`
	Status           string          `json:"status" gorm:"-"` // 狀態 DRAFT SENT REJECT CONFIRM PAID CANCEL
}

func (s *confirmationNoteService) ProfessionalList(db *gorm.DB, req ProfessionalConfirmationNoteListReq, userId uint64, pageSet *xresp.PageSet, sortSet xresp.SortingSet) ([]ProfessionalConfirmationNoteListResp, error) {
	var err error
	var resp []ProfessionalConfirmationNoteListResp

	builder := db.Table("document AS d").
		Joins("JOIN job_application AS ja ON ja.id = d.job_application_id").
		Joins("LEFT JOIN (?) AS d2 ON d2.from_document_id = d.id",
			db.
				Table("document AS invoice").
				Select([]string{
					"invoice.from_document_id",
					"GROUP_CONCAT(invoice.id SEPARATOR ', ') AS related_invoice_id",
				}).
				Where("invoice.category = ?", model.DocumentCategoryInvoice).
				Where("invoice.progress = ?", model.DocumentProgressConfirm).            // 排除已作廢的發票
				Where("invoice.data_type <> ?", model.DocumentDataTypeSystemToFacility). // 排除系統開立給機構的發票
				Group("invoice.from_document_id")).
		Select([]string{
			"d.id AS document_id",
			"d.document_no",
			"d.from_name",
			"d.to_name",
			"d.document_date",
			"d.particular",
			"d.progress",
			"d.grand_total",
			"d2.related_invoice_id",
			fmt.Sprintf("IF(d.data_type = '%s', 'N', 'Y') AS can_edit", model.DocumentDataTypeSystemCompensation),
		}).
		Where("d.user_id = ?", userId).
		Where("d.category = ?", model.DocumentCategoryConfirmation)

	if req.DocumentNo != "" {
		builder = builder.Where("d.document_no LIKE ?", xgorm.EscapeLikeWithWildcards(req.DocumentNo))
	}
	if req.Progress != "" {
		builder = builder.Where("d.progress IN (?)", strings.Split(req.Progress, ","))
	}
	if req.StartDate != "" {
		builder = builder.Where("d.document_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		builder = builder.Where("d.document_date <= ?", req.EndDate)
	}
	if req.ToName != "" {
		builder = builder.Where("d.to_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.ToName))
	}
	if req.Particular != "" {
		builder = builder.Where("d.particular LIKE ?", xgorm.EscapeLikeWithWildcards(req.Particular))
	}
	if req.JobId != 0 {
		builder = builder.Where("ja.job_id = ?", req.JobId)
	}

	sortKeyList := map[string]string{
		"createTime": "d.create_time",
		"amount":     "d.grand_total",
	}

	if err = builder.
		Scopes(xresp.Paginate(pageSet)).
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("d.document_date DESC").
		Order("d.create_time DESC").Find(&resp).Error; err != nil {
		return resp, err
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 專業人士列表 ----------------------------------------------------

// region ---------------------------------------------------- 機構列表 ----------------------------------------------------

type FacilityConfirmationNoteListReq struct {
	DocumentNo string `form:"documentNo"`                                        // 確認通知單號碼
	StartDate  string `form:"startDate" binding:"omitempty,datetime=2006-01-02"` // 開始日期
	EndDate    string `form:"endDate" binding:"omitempty,datetime=2006-01-02"`   // 結束日期
	ToName     string `form:"toName"`                                            // 付款人
	FromName   string `form:"fromName"`
	Particular string `form:"particular"`
	Progress   string `form:"progress"`
	JobId      uint64 `form:"jobId"` // 工作ID
}

type FacilityConfirmationNoteListSummaryResp struct {
	GrandTotal decimal.Decimal                    `json:"grandTotal"`
	List       []FacilityConfirmationNoteListResp `json:"list" gorm:"-"`
}

type FacilityConfirmationNoteListResp struct {
	DocumentId       uint64          `json:"documentId"`
	DocumentNo       string          `json:"documentNo"`
	DocumentDate     xtype.Date      `swaggertype:"string" json:"documentDate"`
	JobApplicationId uint64          `json:"jobApplicationId"`
	ProfessionalId   uint64          `json:"professionalId"`
	Particular       string          `json:"particular"`
	FromName         string          `json:"fromName"` // 發件人姓名
	ToName           string          `json:"toName"`
	GrandTotal       decimal.Decimal `json:"grandTotal"`
	Progress         string          `json:"progress"`
}

func (s *confirmationNoteService) FacilityList(db *gorm.DB, req FacilityConfirmationNoteListReq, facilityId uint64, pageSet *xresp.PageSet, sortSet xresp.SortingSet) (FacilityConfirmationNoteListSummaryResp, error) {
	var err error
	var resp []FacilityConfirmationNoteListResp
	var summary FacilityConfirmationNoteListSummaryResp

	builder := db.Table("document AS d").
		Joins("JOIN job_application AS ja ON ja.id = d.job_application_id").
		Where("d.facility_id = ?", facilityId).
		Where("d.category = ?", model.DocumentCategoryConfirmation).
		Where("d.progress = ? OR d.progress = ?", model.DocumentProgressConfirm, model.DocumentProgressSent)
	if req.DocumentNo != "" {
		builder = builder.Where("d.document_no LIKE ?", xgorm.EscapeLikeWithWildcards(req.DocumentNo))
	}
	if req.Progress != "" {
		builder = builder.Where("d.progress = ?", req.Progress)
	}
	if req.StartDate != "" {
		builder = builder.Where("d.document_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		builder = builder.Where("d.document_date <= ?", req.EndDate)
	}
	if req.ToName != "" {
		builder = builder.Where("d.to_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.ToName))
	}
	if req.FromName != "" {
		builder = builder.Where("d.from_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.FromName))
	}
	if req.Particular != "" {
		builder = builder.Where("d.particular LIKE ?", xgorm.EscapeLikeWithWildcards(req.Particular))
	}
	if req.JobId != 0 {
		builder = builder.Where("ja.job_id = ?", req.JobId)
	}
	totalAmountBuilder := builder.Session(&gorm.Session{})
	if err = totalAmountBuilder.Select("IFNULL(SUM(d.grand_total),0) AS grand_total").Scan(&summary).Error; err != nil {
		return summary, err
	}

	sortKeyList := map[string]string{
		"createTime": "d.create_time",
		"amount":     "d.grand_total",
	}

	if err = builder.
		Select([]string{
			"d.id AS document_id",
			"d.document_no",
			"d.document_date",
			"d.job_application_id",
			"d.professional_id",
			"d.particular",
			"d.from_name",
			"d.to_name",
			"d.grand_total",
			"d.progress",
		}).Scopes(xresp.Paginate(pageSet)).
		Scopes(xresp.AddOrder(sortSet, sortKeyList)).
		Order("d.document_date DESC").
		Order("d.id DESC").Find(&resp).Error; err != nil {
		return summary, err
	}
	summary.List = resp

	return summary, nil
}

// endregion ---------------------------------------------------- 機構列表 ----------------------------------------------------

// region ---------------------------------------------------- 查詢確認通知單 ----------------------------------------------------

type ConfirmationNoteInquireReq struct {
	DocumentId uint64 `form:"documentId" binding:"required"` // 確認通知單ID
}

type ConfirmationNoteInquireResp struct {
	DocumentId                uint64                                  `json:"documentId"`
	DocumentNo                string                                  `json:"documentNo"`
	DocumentDate              xtype.Date                              `swaggertype:"string" json:"documentDate"`
	JobId                     uint64                                  `json:"jobId"`
	JobApplicationId          uint64                                  `json:"jobApplicationId"`
	JobPositionProfession     string                                  `json:"jobPositionProfession"`     // 職位專業
	JobPositionProfessionName string                                  `json:"jobPositionProfessionName"` // 職位專業名稱
	JobBenefitsName           string                                  `json:"jobBenefitsName"`           // 福利名稱
	JobShiftItems             []JobSearchForProfessionalShiftTimeResp `json:"jobShiftItems" gorm:"-"`    // 班次時間
	FromName                  string                                  `json:"fromName"`
	FromAddress               string                                  `json:"fromAddress"`
	FromEmail                 string                                  `json:"fromEmail"`
	FromBankStateBranch       string                                  `json:"fromBankStateBranch"`
	FromBankAccountNumber     string                                  `json:"fromBankAccountNumber"`
	FromAbn                   string                                  `json:"fromAbn"`
	AbnEntityType             string                                  `json:"abnEntityType"`
	ToName                    string                                  `json:"toName"`
	ToAddress                 string                                  `json:"toAddress"`
	ToEmail                   string                                  `json:"toEmail"`
	ToAbn                     string                                  `json:"toAbn"`
	Status                    string                                  `json:"status"`
	TotalAmount               decimal.Decimal                         `json:"totalAmount"`
	TaxAmount                 decimal.Decimal                         `json:"taxAmount"`
	TaxRate                   decimal.Decimal                         `json:"taxRate"`
	SuperRate                 decimal.Decimal                         `json:"superRate"`
	SuperAmount               decimal.Decimal                         `json:"superAmount"`
	CommissionAmount          decimal.Decimal                         `json:"commissionAmount"`
	CommissionItems           []ConfirmationNoteCommissionItemResp    `json:"commissionItems"` // 佣金組成項目
	GrandTotal                decimal.Decimal                         `json:"grandTotal"`
	Remark                    string                                  `json:"remark"`
	OtherRemark               string                                  `json:"otherRemark"`
	RejectReason              string                                  `json:"rejectReason"`
	WagesItem                 ConfirmationNoteItemSummaryInquireResp  `json:"wagesItem"`
	OtherItem                 ConfirmationNoteItemSummaryInquireResp  `json:"otherItem"`
	WagesFiles                []ConfirmationNoteFileInquireResp       `json:"wagesFiles"`
	OtherFiles                []ConfirmationNoteFileInquireResp       `json:"otherFiles"`
	CreateTime                time.Time                               `json:"createTime" swaggertype:"string"` // 創建時間
	UpdateTime                *time.Time                              `json:"updateTime" swaggertype:"string"` // 修改時間
	CanEdit                   string                                  `json:"canEdit"`
	RelatedInvoiceId          string                                  `json:"relatedInvoiceId"`
	ProfessionalGst           string                                  `json:"professionalGst" gorm:"-"` // 此單據的是否包含有JobShift GST的班次
}

type ConfirmationNoteItemSummaryInquireResp struct {
	Items       []ConfirmationNoteItemInquireResp `json:"items"`
	TotalAmount decimal.Decimal                   `json:"totalAmount"`
	TaxAmount   decimal.Decimal                   `json:"taxAmount"`
	SuperAmount decimal.Decimal                   `json:"superAmount"`
	GrandTotal  decimal.Decimal                   `json:"grandTotal"`
}

type ConfirmationNoteItemInquireResp struct {
	DocumentId       uint64          `json:"documentId"`
	ItemType         string          `json:"itemType"`
	ItemName         string          `json:"itemName"`
	AllowanceType    string          `json:"allowanceType"`
	Seq              int32           `json:"seq"`
	JobShiftId       uint64          `json:"jobShiftId"`
	ItemDate         string          `json:"date"`
	StartTime        string          `json:"startTime"`
	NextDay          string          `json:"nextDay"`
	FinishTime       string          `json:"endTime"`
	Particular       string          `json:"particular"`
	ExpectedHours    decimal.Decimal `json:"expectedHours"`
	Hours            decimal.Decimal `json:"hours"`
	HourlyRate       decimal.Decimal `json:"rate"`
	BreakDuration    decimal.Decimal `json:"breakDuration"`
	TaxAmount        decimal.Decimal `json:"taxAmount"`
	TotalAmount      decimal.Decimal `json:"totalAmount"`
	CommissionAmount decimal.Decimal `json:"commissionAmount"`
	CommissionRate   decimal.Decimal `json:"commissionRate"`
	SuperAmount      decimal.Decimal `json:"superAmount"`
	CountTax         string          `json:"countTax"`
}

type ConfirmationNoteFileInquireResp struct {
	DocumentFileId uint64 `json:"documentFileId"`
	Uuid           string `json:"uuid"`
	OriginFileName string `json:"originFileName"`
	FileType       string `json:"fileType"`
	FileSize       uint32 `json:"fileSize"`
}

type ConfirmationNoteCommissionItemResp struct {
	ItemDate         string          `json:"date"`
	TotalAmount      decimal.Decimal `json:"totalAmount"`
	SuperAmount      decimal.Decimal `json:"superAmount"`
	CommissionRate   decimal.Decimal `json:"commissionRate"`
	CommissionAmount decimal.Decimal `json:"commissionAmount"`
}

func (s *confirmationNoteService) Inquire(db *gorm.DB, req ConfirmationNoteInquireReq) (ConfirmationNoteInquireResp, error) {
	var err error
	var resp ConfirmationNoteInquireResp
	var document model.Document

	// 查詢確認通知單
	if err = db.First(&document, req.DocumentId).Error; err != nil {
		return resp, err
	}

	// 查詢工作申請
	var jobApplication model.JobApplication
	if err = db.First(&jobApplication, document.JobApplicationId).Error; err != nil {
		return resp, err
	}

	// 查詢工作
	var job model.Job
	if err = db.First(&job, jobApplication.JobId).Error; err != nil {
		return resp, err
	}

	// 查詢professional
	var professional model.Professional
	if err = db.First(&professional, document.ProfessionalId).Error; err != nil {
		return resp, err
	}

	// 組裝響應
	resp.DocumentId = document.Id
	resp.DocumentNo = document.DocumentNo.String
	resp.DocumentDate = document.DocumentDate
	resp.JobId = jobApplication.JobId
	resp.JobApplicationId = document.JobApplicationId
	resp.FromName = document.FromName
	resp.FromAddress = document.FromAddress
	resp.FromEmail = document.FromEmail
	resp.FromBankStateBranch = document.FromBankStateBranch
	resp.FromBankAccountNumber = document.FromBankAccountNumber
	resp.AbnEntityType = professional.AbnEntityType
	resp.ToName = document.ToName
	resp.ToAddress = document.ToAddress
	resp.ToEmail = document.ToEmail
	resp.ToAbn = document.ToAbn
	resp.Status = document.Progress
	resp.TotalAmount = document.TotalAmount
	resp.TaxAmount = document.TaxAmount
	resp.TaxRate = document.TaxRate
	resp.SuperRate = document.SuperRate
	resp.SuperAmount = document.SuperAmount
	resp.CommissionAmount = document.CommissionAmount
	resp.GrandTotal = document.GrandTotal
	resp.Remark = document.Remark
	resp.OtherRemark = document.OtherRemark
	resp.RejectReason = document.RejectReason
	resp.CreateTime = document.CreateTime
	resp.UpdateTime = document.UpdateTime
	if document.DataType == model.DocumentDataTypeSystemCompensation {
		resp.CanEdit = "N"
	} else {
		resp.CanEdit = "Y"
	}

	resp.RelatedInvoiceId = ""

	resp.JobPositionProfession = job.PositionProfession
	positionProfession, err := SelectionService.FindByCode(db, resp.JobPositionProfession)
	if xgorm.IsSqlErr(err) {
		return resp, err
	}
	if positionProfession != nil {
		resp.JobPositionProfessionName = positionProfession.Name
	}
	// 加載工作班次時間
	var shiftTimes []JobSearchForProfessionalShiftTimeResp
	if err = db.Table("job_shift").
		Select([]string{
			"job_id",
			"begin_time",
			"end_time",
			"duration",
			"break_duration",
			"pay_hours",
			"hourly_rate",
		}).
		Where("job_id = ?", jobApplication.JobId).Order("id").Find(&shiftTimes).Error; err != nil {
		return resp, err
	}
	resp.JobShiftItems = shiftTimes

	// 獲取福利的名稱
	if err = db.Table("benefit AS b").
		Joins("JOIN job_benefit AS jb ON b.id = jb.benefit_id").
		Where("jb.job_id = ?", resp.JobId).
		Select("COALESCE(GROUP_CONCAT(b.name ORDER BY b.id ASC SEPARATOR ', '),'') AS benefits").
		Pluck("benefits", &resp.JobBenefitsName).Error; err != nil {
		return resp, err
	}

	// 查詢項目
	var documentItems []model.DocumentItem
	if err = db.Where("document_id = ?", req.DocumentId).Order("seq ASC").Find(&documentItems).Error; err != nil {
		return resp, err
	}

	resp.WagesItem = ConfirmationNoteItemSummaryInquireResp{
		Items: make([]ConfirmationNoteItemInquireResp, 0),
	}
	resp.OtherItem = ConfirmationNoteItemSummaryInquireResp{
		Items: make([]ConfirmationNoteItemInquireResp, 0),
	}
	for _, documentItem := range documentItems {
		resultItem := ConfirmationNoteItemInquireResp{}

		_ = copier.Copy(&resultItem, documentItem)
		resultItem.ItemDate = documentItem.ItemDate.Date.String()
		if documentItem.ItemType == model.DocumentItemTypeWages ||
			documentItem.ItemType == model.DocumentItemTypeAdditionalWages ||
			documentItem.ItemType == model.DocumentItemTypeAllowance ||
			documentItem.ItemType == model.DocumentItemTypeCancellationFee {
			resp.WagesItem.Items = append(resp.WagesItem.Items, resultItem)
			resp.WagesItem.TotalAmount = resp.WagesItem.TotalAmount.Add(documentItem.TotalAmount)
			resp.WagesItem.TaxAmount = resp.WagesItem.TaxAmount.Add(documentItem.TaxAmount)
			resp.WagesItem.GrandTotal = resp.WagesItem.GrandTotal.Add(documentItem.TotalAmount).Add(documentItem.TaxAmount)
		} else if documentItem.ItemType != model.DocumentItemTypeSuperAmount {
			// 報銷的項目沒有稅和super金額
			resp.OtherItem.Items = append(resp.OtherItem.Items, resultItem)
			resp.OtherItem.TotalAmount = resp.OtherItem.TotalAmount.Add(documentItem.TotalAmount)
			resp.OtherItem.GrandTotal = resp.OtherItem.GrandTotal.Add(documentItem.TotalAmount)
		}
	}

	companyAbn := ProfessionalProfileService.CheckIsCompanyAbn(professional)
	// 不是 Sole Trader 的專業人士，則需要將 Super 金額加到 Wages 部分
	if companyAbn {
		resp.WagesItem.SuperAmount = resp.WagesItem.SuperAmount.Add(document.SuperAmount)
		resp.WagesItem.GrandTotal = resp.WagesItem.GrandTotal.Add(document.SuperAmount)
	}

	// 查詢文件
	var documentFiles []model.DocumentFile
	if err = db.
		Table("document_file AS df").
		Joins("JOIN document_file_relation AS dfr ON dfr.document_file_id = df.id").
		Select([]string{
			"df.*",
		}).
		Where("dfr.document_id = ?", req.DocumentId).
		Order("dfr.seq ASC").
		Find(&documentFiles).Error; err != nil {
		return resp, err
	}
	resp.WagesFiles = make([]ConfirmationNoteFileInquireResp, 0)
	resp.OtherFiles = make([]ConfirmationNoteFileInquireResp, 0)
	for _, documentFile := range documentFiles {
		resultFile := ConfirmationNoteFileInquireResp{}

		_ = copier.Copy(&resultFile, documentFile)
		resultFile.DocumentFileId = documentFile.Id

		if documentFile.FileCode == model.DocumentFileCodeWages {
			resp.WagesFiles = append(resp.WagesFiles, resultFile)
		} else {
			resp.OtherFiles = append(resp.OtherFiles, resultFile)
		}
	}

	return resp, nil
}

type ConfirmationNoteProfessionalInquireResp struct {
	DocumentId                uint64                                             `json:"documentId"`
	DocumentNo                string                                             `json:"documentNo"`
	DocumentDate              xtype.Date                                         `swaggertype:"string" json:"documentDate"`
	JobId                     uint64                                             `json:"jobId"`
	JobApplicationId          uint64                                             `json:"jobApplicationId"`
	JobPositionProfession     string                                             `json:"jobPositionProfession"`     // 職位專業
	JobPositionProfessionName string                                             `json:"jobPositionProfessionName"` // 職位專業名稱
	JobBenefitsName           string                                             `json:"jobBenefitsName"`           // 福利名稱
	JobShiftItems             []JobSearchForProfessionalShiftTimeResp            `json:"jobShiftItems" gorm:"-"`    // 班次時間
	FromName                  string                                             `json:"fromName"`
	FromAddress               string                                             `json:"fromAddress"`
	FromEmail                 string                                             `json:"fromEmail"`
	FromBankStateBranch       string                                             `json:"fromBankStateBranch"`
	FromBankAccountNumber     string                                             `json:"fromBankAccountNumber"`
	FromAbn                   string                                             `json:"FromAbn"`
	AbnEntityType             string                                             `json:"abnEntityType"`
	ToName                    string                                             `json:"toName"`
	ToAddress                 string                                             `json:"toAddress"`
	ToEmail                   string                                             `json:"toEmail"`
	ToAbn                     string                                             `json:"toAbn"`
	Status                    string                                             `json:"status"`
	TotalAmount               decimal.Decimal                                    `json:"totalAmount"`
	TaxAmount                 decimal.Decimal                                    `json:"taxAmount"`
	TaxRate                   decimal.Decimal                                    `json:"taxRate"`
	SuperRate                 decimal.Decimal                                    `json:"superRate"`
	SuperAmount               decimal.Decimal                                    `json:"superAmount"`
	GrandTotal                decimal.Decimal                                    `json:"grandTotal"`
	Remark                    string                                             `json:"remark"`
	OtherRemark               string                                             `json:"otherRemark"`
	RejectReason              string                                             `json:"rejectReason"`
	WagesItem                 ConfirmationNoteItemSummaryProfessionalInquireResp `json:"wagesItem"`
	OtherItem                 ConfirmationNoteItemSummaryProfessionalInquireResp `json:"otherItem"`
	WagesFiles                []ConfirmationNoteFileInquireResp                  `json:"wagesFiles"`
	OtherFiles                []ConfirmationNoteFileInquireResp                  `json:"otherFiles"`
	CreateTime                time.Time                                          `json:"createTime" swaggertype:"string"` // 創建時間
	UpdateTime                *time.Time                                         `json:"updateTime" swaggertype:"string"` // 修改時間
	CanEdit                   string                                             `json:"canEdit"`
	RelatedInvoiceId          string                                             `json:"relatedInvoiceId"`
	ProfessionalGst           string                                             `json:"professionalGst" gorm:"-"` // 此單據的是否包含有JobShift GST的班次
}

type ConfirmationNoteItemSummaryProfessionalInquireResp struct {
	Items       []ConfirmationNoteItemProfessionalInquireResp `json:"items"`
	TotalAmount decimal.Decimal                               `json:"totalAmount"`
	TaxAmount   decimal.Decimal                               `json:"taxAmount"`
	SuperAmount decimal.Decimal                               `json:"superAmount"`
	GrandTotal  decimal.Decimal                               `json:"grandTotal"`
}

type ConfirmationNoteItemProfessionalInquireResp struct {
	DocumentId    uint64          `json:"documentId" `
	ItemType      string          `json:"itemType" `
	ItemName      string          `json:"itemName" `
	AllowanceType string          `json:"allowanceType"`
	Seq           int32           `json:"seq"`
	JobShiftId    uint64          `json:"jobShiftId"`
	ItemDate      string          `json:"date"`
	StartTime     string          `json:"startTime"`
	NextDay       string          `json:"nextDay"`
	FinishTime    string          `json:"endTime"`
	Particular    string          `json:"particular"`
	ExpectedHours decimal.Decimal `json:"expectedHours"`
	Hours         decimal.Decimal `json:"hours"`
	HourlyRate    decimal.Decimal `json:"rate"`
	BreakDuration decimal.Decimal `json:"breakDuration"`
	TaxAmount     decimal.Decimal `json:"taxAmount"`
	TotalAmount   decimal.Decimal `json:"totalAmount"`
	SuperAmount   decimal.Decimal `json:"superAmount"`
	CountTax      string          `json:"countTax"`
}

func (s *confirmationNoteService) ProfessionalInquire(db *gorm.DB, req ConfirmationNoteInquireReq) (ConfirmationNoteProfessionalInquireResp, error) {
	var err error
	var resp ConfirmationNoteProfessionalInquireResp

	inquireResp, err := s.Inquire(db, req)
	if err != nil {
		return resp, err
	}

	_ = copier.Copy(&resp, &inquireResp)

	// 獲取 關聯的 Invoice Id
	relatedInvoiceResult, err := s.GetRelatedInvoice(db, resp.DocumentId, "PROFESSIONAL")
	if err != nil {
		return resp, err
	}

	resp.ProfessionalGst = "N"
	dates := make([]string, 0)
	for _, item := range inquireResp.WagesItem.Items {
		if item.ItemType == model.DocumentItemTypeWages && item.ItemDate != "" {
			dates = append(dates, item.ItemDate)
		}
	}
	if len(dates) > 0 {
		resp.ProfessionalGst, err = s.GetDocumentProfessionalGst(db, resp.DocumentId, dates)
		if err != nil {
			return resp, err
		}
	}

	resp.RelatedInvoiceId = relatedInvoiceResult.RelatedInvoiceId
	return resp, nil
}

func (s *confirmationNoteService) FacilityInquire(db *gorm.DB, req ConfirmationNoteInquireReq) (ConfirmationNoteInquireResp, error) {
	resp, err := s.Inquire(db, req)
	if err != nil {
		return resp, err
	}
	// 獲取 關聯的 Invoice Id
	relatedInvoiceResult, err := s.GetRelatedInvoice(db, resp.DocumentId, "FACILITY")
	if err != nil {
		return resp, err
	}
	resp.RelatedInvoiceId = relatedInvoiceResult.RelatedInvoiceId

	resp.CommissionItems = make([]ConfirmationNoteCommissionItemResp, 0)

	if resp.CommissionAmount.GreaterThan(decimal.Zero) {
		// 如果有佣金金額，則需要查詢佣金組成項目
		for _, item := range resp.WagesItem.Items {
			if item.CommissionAmount.GreaterThan(decimal.Zero) {
				var commissionItem ConfirmationNoteCommissionItemResp
				commissionItem.ItemDate = item.ItemDate
				commissionItem.TotalAmount = item.TotalAmount
				commissionItem.SuperAmount = decimal.Zero
				commissionItem.CommissionRate = item.CommissionRate
				commissionItem.CommissionAmount = commissionItem.TotalAmount.Mul(commissionItem.CommissionRate).Round(2)
				resp.CommissionItems = append(resp.CommissionItems, commissionItem)
			}
		}
	}

	resp.ProfessionalGst = "N"
	dates := make([]string, 0)
	for _, item := range resp.WagesItem.Items {
		if item.ItemType == model.DocumentItemTypeWages && item.ItemDate != "" {
			dates = append(dates, item.ItemDate)
		}
	}
	if len(dates) > 0 {
		resp.ProfessionalGst, err = s.GetDocumentProfessionalGst(db, resp.DocumentId, dates)
		if err != nil {
			return resp, err
		}
	}
	return resp, nil
}

func (s *confirmationNoteService) SystemInquire(db *gorm.DB, req ConfirmationNoteInquireReq) (ConfirmationNoteInquireResp, error) {
	resp, err := s.Inquire(db, req)
	if err != nil {
		return resp, err
	}
	// 獲取 關聯的 Invoice Id
	relatedInvoiceResult, err := s.GetRelatedInvoice(db, resp.DocumentId, "SYSTEM")
	if err != nil {
		return resp, err
	}
	resp.RelatedInvoiceId = relatedInvoiceResult.RelatedInvoiceId

	resp.CommissionItems = make([]ConfirmationNoteCommissionItemResp, 0)

	if resp.CommissionAmount.GreaterThan(decimal.Zero) {
		// 如果有佣金金額，則需要查詢佣金組成項目
		for _, item := range resp.WagesItem.Items {
			if item.CommissionAmount.GreaterThan(decimal.Zero) {
				var commissionItem ConfirmationNoteCommissionItemResp
				commissionItem.ItemDate = item.ItemDate
				commissionItem.TotalAmount = item.TotalAmount
				commissionItem.SuperAmount = decimal.Zero
				commissionItem.CommissionRate = item.CommissionRate
				commissionItem.CommissionAmount = commissionItem.TotalAmount.Mul(commissionItem.CommissionRate).Round(2)
				resp.CommissionItems = append(resp.CommissionItems, commissionItem)
			}
		}
	}

	resp.ProfessionalGst = "N"
	dates := make([]string, 0)
	for _, item := range resp.WagesItem.Items {
		if item.ItemType == model.DocumentItemTypeWages && item.ItemDate != "" {
			dates = append(dates, item.ItemDate)
		}
	}
	if len(dates) > 0 {
		resp.ProfessionalGst, err = s.GetDocumentProfessionalGst(db, resp.DocumentId, dates)
		if err != nil {
			return resp, err
		}
	}
	return resp, nil
}

func (s *confirmationNoteService) GetDocumentProfessionalGst(db *gorm.DB, documentId uint64, dates []string) (string, error) {
	var document model.Document
	if err := db.Model(&model.Document{}).
		Where("id = ?", documentId).
		First(&document).Error; err != nil {
		return "", err
	}
	// 查詢專業人士GST記錄（多個日期）是否存在
	findDate, err := ProfessionalGstService.ProfessionalGstSomeDates(db, document.ProfessionalId, dates)
	if err != nil {
		return "", err
	}
	if findDate {
		return "Y", nil
	} else {
		return "N", nil
	}
}

type ConfirmationNoteRelatedInvoice struct {
	FromDocumentId   uint64 `json:"fromDocumentId"`   // 確認通知單ID
	RelatedInvoiceId string `json:"relatedInvoiceId"` // 關聯的發票ID
}

func (s *confirmationNoteService) GetRelatedInvoice(db *gorm.DB, documentId uint64, forType string) (ConfirmationNoteRelatedInvoice, error) {
	// 獲取 關聯的 Invoice Id
	var resp ConfirmationNoteRelatedInvoice

	builder := db.Table("document AS invoice").
		Select([]string{
			"invoice.from_document_id",
			"COALESCE(GROUP_CONCAT(invoice.id SEPARATOR ','),'') AS related_invoice_id",
		}).
		Where("invoice.category = ?", model.DocumentCategoryInvoice).
		Where("invoice.progress = ?", model.DocumentProgressConfirm).                // 排除已作廢的發票
		Where("invoice.data_type <> ?", model.DocumentDataTypeProfessionalToSystem). // 排除專業人士開立給系統的發票
		Where("invoice.from_document_id = ?", documentId)                            // 僅查詢與當前確認通知單相關的發票
	if forType == "PROFESSIONAL" {
		builder = builder.Where("invoice.data_type <> ?", model.DocumentDataTypeSystemToFacility) // 排除系統開立給機構的發票
	} else if forType == "FACILITY" {
		builder = builder.Where("invoice.data_type <> ?", model.DocumentDataTypeProfessionalToSystem) // 排除專業人士開立給系統的發票
	}

	if err := builder.
		Group("invoice.from_document_id").
		Scan(&resp).Error; err != nil {
		return resp, err
	}
	return resp, nil
}

// endregion ---------------------------------------------------- 查詢確認通知單 ----------------------------------------------------

// region ---------------------------------------------------- 創建確認通知單 ----------------------------------------------------

type ConfirmationNoteCreateReq struct {
	JobApplicationId      uint64                         `json:"jobApplicationId" binding:"required"`
	FromName              string                         `json:"fromName" binding:"required"`
	FromBankStateBranch   string                         `json:"fromBankStateBranch" binding:"required"`
	FromBankAccountNumber string                         `json:"fromBankAccountNumber" binding:"required"`
	FromBankAccountName   string                         `json:"fromBankAccountName" binding:"required"`
	ToName                string                         `json:"toName" binding:"required"`
	ToAddress             string                         `json:"toAddress" binding:"required"`
	ToAbn                 string                         `json:"toAbn" binding:"required"`
	WagesItem             ConfirmationNoteItemSummaryReq `json:"wagesItem" binding:"required"`
	OtherItem             ConfirmationNoteItemSummaryReq `json:"otherItem" binding:"required"`
	Remark                string                         `json:"remark"`
	OtherRemark           string                         `json:"otherRemark"`
	WagesDocumentFileIds  []uint64                       `json:"wagesDocumentFileIds"`
	OtherDocumentFileIds  []uint64                       `json:"otherDocumentFileIds"`
	TotalAmount           decimal.Decimal                `json:"totalAmount"`
	TaxAmount             decimal.Decimal                `json:"taxAmount"`
	SuperAmount           decimal.Decimal                `json:"superAmount"`
	GrandTotal            decimal.Decimal                `json:"grandTotal"`
}

type ConfirmationNoteItemSummaryReq struct {
	Items       []ConfirmationNoteItemReq `json:"items" binding:"required,dive"`
	TotalAmount decimal.Decimal           `json:"totalAmount"` // 合計
	TaxAmount   decimal.Decimal           `json:"taxAmount"`   // 稅金
	SuperAmount decimal.Decimal           `json:"superAmount"` // super 金額
	GrandTotal  decimal.Decimal           `json:"grandTotal"`  // 總計
}

type ConfirmationNoteItemReq struct {
	ItemDate      string          `json:"itemDate" binding:"required_unless=ItemType ALLOWANCE,omitempty,datetime=2006-01-02"`                                                              // 日期(YYYY-MM-DD)
	ItemType      string          `json:"itemType" binding:"required_without=AllowanceId,oneof=WAGES ADDITIONAL_WAGES TRAVEL_REIMBURSEMENT MEALS ACCOMMODATION ALLOWANCE CANCELLATION_FEE"` // 項目類型 WAGES 工資 ADDITIONAL_WAGES 加班費 TRAVEL_REIMBURSEMENT 差旅費 MEALS 餐費 ACCOMMODATION 住宿費 ALLOWANCE 津貼 CANCELLATION_FEE 賠付
	ItemName      string          `json:"itemName" binding:"required_if=ItemType ALLOWANCE"`                                                                                                // 津貼名稱
	AllowanceType string          `json:"allowanceType" binding:"required_if=ItemType ALLOWANCE,omitempty,oneof=HOURLY SHIFT JOB"`
	JobShiftId    uint64          `json:"jobShiftId" binding:"required_if=ItemType WAGES"` // 工作班次ID
	StartTime     string          `json:"startTime"`                                       // 開始時間(HH:MM)
	NextDay       string          `json:"nextDay" binding:"required,oneof=Y N"`            // 是否跨天 Y N
	FinishTime    string          `json:"finishTime"`                                      // 結束時間(HH:MM)
	Particular    string          `json:"particular" binding:"required"`                   // 備註
	ExpectedHours decimal.Decimal `json:"expectedHours"`                                   // 預計工時
	Hours         decimal.Decimal `json:"hours"`                                           // 工時
	HourlyRate    decimal.Decimal `json:"hourlyRate"`                                      // 時薪
	BreakDuration decimal.Decimal `json:"breakDuration"`                                   // 休息時長
	TotalAmount   decimal.Decimal `json:"totalAmount" binding:"required"`                  // 總金額（不包含退休金和稅額）
	TaxAmount     decimal.Decimal `json:"taxAmount" `                                      // 稅金
	SuperAmount   decimal.Decimal `json:"superAmount" `                                    // 退休金金額
	CountTax      string          `json:"countTax" binding:"required,oneof=Y N"`           // 是否計算稅額 Y N
}

type ConfirmationNoteCreateResp struct {
	DocumentId uint64 `json:"documentId"`
}

func (s *confirmationNoteService) Create(db *gorm.DB, req ConfirmationNoteCreateReq, superRate decimal.Decimal, taxRate decimal.Decimal, jobApplication model.JobApplication, professional model.Professional) (ConfirmationNoteCreateResp, error) {
	nowTime := time.Now().UTC().Truncate(time.Second)
	var resp ConfirmationNoteCreateResp
	var err error

	// 查詢工作
	var job model.Job
	if err = db.First(&job, jobApplication.JobId).Error; err != nil {
		return resp, err
	}

	// 查詢專業人士用戶信息
	var professionalUser xmodel.User
	if err = db.Where("id = ?", professional.UserId).
		First(&professionalUser).Error; err != nil {
		return resp, err
	}

	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", job.FacilityId).
		First(&facilityProfile, job.FacilityProfileId).Error; err != nil {
		return resp, err
	}

	// DataType 根據機構協議的類型來決定
	dataType := model.DocumentDataTypeProfessionalToFacility
	if facilityProfile.PaymentTerms == model.FacilityProfilePaymentTermsPayUpfront {
		dataType = model.DocumentDataTypeProfessionalToSystem
	}

	documentNo, seqNo, err := s.GenerateDocumentNo(db, model.DocumentCategoryConfirmation, dataType, job, jobApplication, professional.Id)
	if err != nil {
		return resp, err
	}

	var serviceLocation model.ServiceLocation
	if err = db.First(&serviceLocation, job.ServiceLocationId).Error; err != nil {
		return resp, err
	}
	location, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return resp, err
	}

	documentItems, particulars := s.generateItems(req.WagesItem, req.OtherItem, location)

	// 計算佣金
	commissionAmount, err := s.CalculateCommissionAmount(db, dataType, documentItems, facilityProfile.FacilityId)
	if err != nil {
		return resp, err
	}

	today := time.Now().Format(xtool.DateDayA) // 服務器的時區(用戶的時區)

	// 創建確認通知單
	document := model.Document{
		Category:              model.DocumentCategoryConfirmation,
		JobApplicationId:      req.JobApplicationId,
		DataType:              dataType,
		SeqNo:                 seqNo,
		DocumentNo:            xtype.NotNullString(documentNo),
		DocumentDate:          xtype.NewDate(today),
		DueDate:               xtype.NewNullDate(),
		FacilityId:            job.FacilityId,
		ProfessionalId:        professional.Id,
		UserId:                professional.UserId,
		FromName:              req.FromName,
		FromEmail:             professionalUser.Email,
		FromAddress:           professional.Address,
		FromBankStateBranch:   req.FromBankStateBranch,
		FromBankAccountNumber: req.FromBankAccountNumber,
		FromBankAccountName:   req.FromBankAccountName,
		FromAbn:               professional.AbnNumber,
		ToName:                req.ToName,
		ToEmail:               facilityProfile.Email,
		ToAddress:             req.ToAddress,
		ToAbn:                 req.ToAbn,
		Particular:            particulars,
		TotalAmount:           req.TotalAmount,
		TaxAmount:             req.TaxAmount,
		TaxRate:               taxRate,
		SuperRate:             superRate,
		SuperAmount:           req.SuperAmount,
		CommissionAmount:      commissionAmount,
		GrandTotal:            req.GrandTotal,
		Progress:              model.DocumentProgressDraft,
		CreateTime:            nowTime,
		CreateUserId:          professional.UserId,
		Remark:                req.Remark,
		OtherRemark:           req.OtherRemark,
	}

	// 保存確認通知單
	if err = db.Create(&document).Error; err != nil {
		return resp, err
	}

	// Professional 完成工作後，要提示可以生成確認單 - 通知Professional
	if err = SystemNotificationService.CreateProfessionalBillingConfirmationNoteGenerate(db, CreateProfessionalBillingConfirmationNoteGenerateReq{
		UserId:       jobApplication.ProfessionalId,
		JobId:        jobApplication.JobId,
		JobTitle:     job.PositionProfession,
		FacilityName: facilityProfile.Name,
	}); err != nil {
		return resp, err
	}

	// 存儲
	for _, documentItem := range documentItems {
		documentItem.DocumentId = document.Id
	}

	if err = db.Create(&documentItems).Error; err != nil {
		return resp, err
	}

	// 存儲附件
	for index, fileId := range req.WagesDocumentFileIds {
		db.Create(&model.DocumentFileRelation{
			DocumentId:     document.Id,
			DocumentFileId: fileId,
			Seq:            int32(index + 1),
		})
	}

	for index, fileId := range req.OtherDocumentFileIds {
		db.Create(&model.DocumentFileRelation{
			DocumentId:     document.Id,
			DocumentFileId: fileId,
			Seq:            int32(index + 1),
		})
	}

	resp.DocumentId = document.Id
	return resp, nil
}

func (s *confirmationNoteService) GetDocumentLastSeqNo(db *gorm.DB, category string, dataType string, jobApplicationId uint64, professionalId uint64) (int32, error) {
	var seqNo int32

	builder := db.Table("document AS d").
		Select([]string{
			"IFNULL(max(seq_no), 0) AS seq_no",
		}).
		Where("d.category = ?", category).
		Where("d.data_type = ?", dataType).
		Where("d.job_application_id = ?", jobApplicationId).
		Where("d.professional_id = ?", professionalId)
	if err := builder.Scan(&seqNo).Error; err != nil {
		return 0, err
	}
	return seqNo, nil
}

func (s *confirmationNoteService) CheckDocumentNo(db *gorm.DB, category string, dataType string, documentNo string) (bool, error) {
	var document model.Document

	if err := db.Table("document").Where("document_no = ?", documentNo).Where("category = ?", category).Where("data_type = ?", dataType).Scan(&document).Error; err != nil {
		return false, err
	}
	if document.Id > 0 {
		return true, nil
	}
	return false, nil
}

func (s *confirmationNoteService) GenerateDocumentNo(db *gorm.DB, category string, dataType string, job model.Job, jobApplication model.JobApplication, professionalId uint64) (string, int32, error) {
	lastSeqNo, err := s.GetDocumentLastSeqNo(db, category, dataType, jobApplication.Id, professionalId)

	if err != nil {
		return "", 0, err
	}

	preDocumentNo := job.JobNo

	var jobApplications []model.JobApplication
	builder := db.
		Where("job_id = ?", job.Id).
		Where("accept = ?", model.JobApplicationAcceptY).
		Where("status = ?", model.JobApplicationStatusAccept).
		Where("accept_time < ? OR (accept_time = ? AND id < ?)", jobApplication.AcceptTime, jobApplication.AcceptTime, jobApplication.Id)

	if err = builder.Find(&jobApplications).Error; err != nil {
		return "", 0, err
	}

	acceptIndex := len(jobApplications) + 1

	if acceptIndex > 10 {
		preDocumentNo = fmt.Sprintf("%s%d", preDocumentNo, acceptIndex)
	} else {
		preDocumentNo = fmt.Sprintf("%s0%d", preDocumentNo, acceptIndex)
	}

	lastSeqNo += 1
	documentNo := fmt.Sprintf("%s#%d", preDocumentNo, lastSeqNo)
	// 循環檢查
	for {
		if lastSeqNo >= 10 {
			documentNo = fmt.Sprintf("%s#%d", preDocumentNo, lastSeqNo)
		} else {
			documentNo = fmt.Sprintf("%s#0%d", preDocumentNo, lastSeqNo)
		}
		exist, err := s.CheckDocumentNo(db, category, dataType, documentNo)
		if err != nil {
			return "", 0, err
		}
		if !exist {
			break
		}
		lastSeqNo += 1
	}

	return documentNo, lastSeqNo, nil
}

func (s *confirmationNoteService) CalculateCommissionAmount(db *gorm.DB, dataType string, documentItems []*model.DocumentItem, facilityId uint64) (decimal.Decimal, error) {
	commissionAmount := decimal.Zero

	for _, item := range documentItems {
		if item.ItemType == model.DocumentItemTypeWages || item.ItemType == model.DocumentItemTypeAdditionalWages {
			commissionSetting, err := FacilityAgreementService.GetCommissionByFacilityIdAndPeriod(db, facilityId, item.ItemDate.String())
			if err != nil {
				return decimal.Zero, err
			}

			commissionRate := decimal.Zero
			if dataType == model.DocumentDataTypeProfessionalToFacility {
				// 專業人員開立給機構， 佣金 = 總金額(ItemType = DocumentItemTypeWages 和 ItemType = DocumentItemTypeAdditionalWages 的總金額，不包含稅) * 機構的佣金率 （事後支付佣金率）
				commissionRate = commissionSetting.PayInArrearsCommissionRate
			} else {
				// 專業人員開立給系統， 佣金 = 金額(ItemType = DocumentItemTypeWages 和 ItemType = DocumentItemTypeAdditionalWages 的總金額, 不包含稅) * 機構的佣金率 （預付款佣金率）
				commissionRate = commissionSetting.PayUpfrontCommissionRate
			}

			commissionAmount = commissionAmount.Add(item.TotalAmount.Mul(commissionRate).Round(2))

			item.CommissionAmount = item.TotalAmount.Mul(commissionRate).Round(2)
			item.CommissionRate = commissionRate
		}
	}
	return commissionAmount, nil
}

type CheckItemValidReq struct {
	WagesItem   ConfirmationNoteItemSummaryReq `json:"wagesItem" binding:"required,dive"`
	OtherItem   ConfirmationNoteItemSummaryReq `json:"otherItem" binding:"required,dive"`
	TotalAmount decimal.Decimal                `json:"totalAmount"`
	TaxAmount   decimal.Decimal                `json:"taxAmount"`
	SuperAmount decimal.Decimal                `json:"superAmount"`
	GrandTotal  decimal.Decimal                `json:"grandTotal"`
}

type ItemSummary struct {
	TotalAmount decimal.Decimal `json:"totalAmount"`
	TaxAmount   decimal.Decimal `json:"taxAmount"`
	SuperAmount decimal.Decimal `json:"superAmount"`
	GrandTotal  decimal.Decimal `json:"grandTotal"`
}

func (s *confirmationNoteService) CheckItemValid(req CheckItemValidReq, superRate decimal.Decimal, taxRate decimal.Decimal, professional model.Professional) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.confirmation_note.invalid_amount_summary",
		Other: "Invalid amount summary.",
	}

	companyAbn := ProfessionalProfileService.CheckIsCompanyAbn(professional)
	wagesPass, wagesSummary := s.CheckItems(req.WagesItem, superRate, taxRate, companyAbn)
	if !wagesPass {
		return false, msg, nil
	}

	otherPass, OtherSummary := s.CheckItems(req.OtherItem, superRate, taxRate, companyAbn)
	if !otherPass {
		return false, msg, nil
	}

	if !wagesSummary.TotalAmount.Add(OtherSummary.TotalAmount).Equal(req.TotalAmount) ||
		!wagesSummary.TaxAmount.Add(OtherSummary.TaxAmount).Equal(req.TaxAmount) ||
		!wagesSummary.SuperAmount.Add(OtherSummary.SuperAmount).Equal(req.SuperAmount) ||
		!wagesSummary.GrandTotal.Add(OtherSummary.GrandTotal).Equal(req.GrandTotal) {
		return false, msg, nil
	}

	return true, msg, nil
}

func (s *confirmationNoteService) CheckItems(req ConfirmationNoteItemSummaryReq, superRate decimal.Decimal, taxRate decimal.Decimal, isCompanyAbn bool) (bool, ItemSummary) {
	var itemSummary ItemSummary

	// 計算 Subtotal
	for _, item := range req.Items {
		amount := decimal.Zero
		if item.Hours.GreaterThan(decimal.Zero) && item.HourlyRate.GreaterThan(decimal.Zero) {
			amount = item.Hours.Mul(item.HourlyRate)
		} else {
			amount = item.TotalAmount
		}

		itemSummary.TotalAmount = itemSummary.TotalAmount.Add(amount)

		itemSuperAmount := decimal.Zero
		if item.ItemType == model.DocumentItemTypeWages || item.ItemType == model.DocumentItemTypeAdditionalWages {
			itemSuperAmount = amount.Mul(superRate).Round(2)
		} else if item.ItemType == model.DocumentItemTypeAllowance {
			itemSuperAmount = item.SuperAmount
		}
		itemSummary.SuperAmount = itemSummary.SuperAmount.Add(itemSuperAmount)

		itemTaxAmount := decimal.Zero
		if item.CountTax == "Y" {
			itemTaxAmount = amount.Mul(taxRate).Round(2)
		}
		itemSummary.TaxAmount = itemSummary.TaxAmount.Add(itemTaxAmount)

		if isCompanyAbn {
			itemSummary.GrandTotal = itemSummary.GrandTotal.Add(amount).Add(itemSuperAmount).Add(itemTaxAmount)
		} else {
			itemSummary.GrandTotal = itemSummary.GrandTotal.Add(amount).Add(itemTaxAmount)
		}
	}

	if !itemSummary.TotalAmount.Equal(req.TotalAmount) ||
		!itemSummary.TaxAmount.Equal(req.TaxAmount) ||
		!itemSummary.SuperAmount.Equal(req.SuperAmount) ||
		!itemSummary.GrandTotal.Equal(req.GrandTotal) {
		return false, itemSummary
	}
	return true, itemSummary
}

func (s *confirmationNoteService) CreateCompensationByJobShift(db *gorm.DB, jobApplication model.JobApplication, jobShift model.JobShift, compensatedHours decimal.Decimal, particular string) error {
	nowTime := time.Now().UTC().Truncate(time.Second)
	var err error

	// 查詢工作
	var job model.Job
	if err = db.First(&job, jobApplication.JobId).Error; err != nil {
		return err
	}

	// 查詢專業人士
	var professional model.Professional
	if err = db.Where("id = ?", jobApplication.ProfessionalId).
		First(&professional).Error; err != nil {
		return err
	}

	var professionalUser xmodel.User
	if err = db.Where("id = ?", professional.UserId).
		First(&professionalUser).Error; err != nil {
		return err
	}

	// 銀行賬戶
	var professionalBankAccount model.ProfessionalBankAccount
	if err = db.Where("user_id = ?", professional.UserId).First(&professionalBankAccount).Error; xgorm.IsSqlErr(err) {
		return err
	}

	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", job.FacilityId).
		First(&facilityProfile, job.FacilityProfileId).Error; err != nil {
		return err
	}

	// DataType 根據機構協議的類型來決定
	dataType := model.DocumentDataTypeSystemCompensation

	documentNo, seqNo, err := s.GenerateDocumentNo(db, model.DocumentCategoryConfirmation, dataType, job, jobApplication, professional.Id)
	if err != nil {
		return err
	}

	var serviceLocation model.ServiceLocation
	if err = db.First(&serviceLocation, job.ServiceLocationId).Error; err != nil {
		return err
	}
	location, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return err
	}

	item := ConfirmationNoteItemReq{
		ItemDate:   jobShift.BeginTime.Format(xtool.DateDayA),
		ItemType:   model.DocumentItemTypeCancellationFee,
		JobShiftId: jobShift.Id,
		Particular: particular,
		TaxAmount:  decimal.Zero,
		CountTax:   "N",
	}

	// 指定 賠付的小時數，則安裝 指定的小時數，否是按照 shift time 的總時數
	if compensatedHours.GreaterThan(decimal.Zero) {
		item.Hours = compensatedHours
		item.TotalAmount = jobShift.HourlyRate.Mul(item.Hours)
	} else {
		item.Hours = jobShift.PayHours
		item.TotalAmount = jobShift.HourlyRate.Mul(item.Hours)
	}

	if jobShift.BeginTime.Format(xtool.DateDayA) != jobShift.EndTime.Format(xtool.DateDayA) {
		item.NextDay = "Y"
	}
	itemSummary := ConfirmationNoteItemSummaryReq{
		Items: []ConfirmationNoteItemReq{
			item,
		},
		TotalAmount: item.TotalAmount,
		TaxAmount:   decimal.Zero,
		SuperAmount: decimal.Zero,
		GrandTotal:  item.TotalAmount,
	}
	documentItems, particulars := s.generateItems(itemSummary, ConfirmationNoteItemSummaryReq{}, location)

	today := time.Now().Format(xtool.DateDayA) // 服務器的時區(用戶的時區)

	// 創建確認通知單
	document := model.Document{
		Category:              model.DocumentCategoryConfirmation,
		JobApplicationId:      jobApplication.Id,
		DataType:              dataType,
		SeqNo:                 seqNo,
		DocumentNo:            xtype.NotNullString(documentNo),
		DocumentDate:          xtype.NewDate(today),
		DueDate:               xtype.NewNullDate(),
		FacilityId:            job.FacilityId,
		ProfessionalId:        professional.Id,
		UserId:                professional.UserId,
		FromName:              fmt.Sprintf("%s %s", professional.FirstName, professional.LastName),
		FromEmail:             professionalUser.Email,
		FromAddress:           professional.Address,
		FromBankStateBranch:   professionalBankAccount.BankStateBranch,
		FromBankAccountNumber: professionalBankAccount.BankAccountNumber,
		FromBankAccountName:   professionalBankAccount.BankAccountName,
		FromAbn:               professional.AbnNumber,
		ToName:                facilityProfile.Name,
		ToEmail:               facilityProfile.Email,
		ToAddress:             facilityProfile.Address,
		ToAbn:                 facilityProfile.Abn,
		Particular:            particulars,
		TotalAmount:           itemSummary.TotalAmount,
		TaxAmount:             decimal.Zero,
		TaxRate:               decimal.Zero,
		SuperRate:             decimal.Zero,
		SuperAmount:           decimal.Zero,
		CommissionAmount:      decimal.Zero,
		GrandTotal:            itemSummary.TotalAmount,
		Progress:              model.DocumentProgressDraft,
		CreateTime:            nowTime,
		CreateUserId:          professional.UserId,
	}

	// 保存確認通知單
	if err = db.Create(&document).Error; err != nil {
		return err
	}

	// 存儲
	for _, documentItem := range documentItems {
		documentItem.DocumentId = document.Id
	}

	if err = db.Create(&documentItems).Error; err != nil {
		return err
	}

	// 工作在開始前1小時後或開始後4小時內取消，符合賠付條件，系統自動生成confirmation note，提醒professional前往提交 - 通知Professional
	compensationAmount, _ := itemSummary.TotalAmount.Float64()
	err = SystemNotificationService.CreateProfessionalJobCompensation(db, CreateProfessionalJobCompensationReq{
		UserId:             jobApplication.ProfessionalId,
		JobId:              jobApplication.JobId,
		ConfirmationNoteId: document.Id,
		FacilityName:       facilityProfile.Name,
		CompensationAmount: compensationAmount,
	})
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 創建確認通知單 ----------------------------------------------------

// 合計單據項目
type ConfirmationNoteSum struct {
	TotalAmount decimal.Decimal `json:"totalAmount"` // 總金額 （Subtotal，未計算稅金）
	SuperAmount decimal.Decimal `json:"superAmount"` // Super 金額
	SuperRate   decimal.Decimal `json:"superRate"`   // Super 稅率
	TaxAmount   decimal.Decimal `json:"taxAmount"`   // 稅金
	TaxRate     decimal.Decimal `json:"taxRate"`     // 稅率
	GrandTotal  decimal.Decimal `json:"grandTotal"`  // 總金額 （Subtotal + Super + Tax）
}

func (s *confirmationNoteService) generateItems(wagesItem ConfirmationNoteItemSummaryReq, otherItem ConfirmationNoteItemSummaryReq, timeLocation *time.Location) ([]*model.DocumentItem, string) {
	var particulars string
	documentItems := make([]*model.DocumentItem, 0)

	seq := int32(1)
	for _, item := range wagesItem.Items {
		var documentItem model.DocumentItem
		_ = copier.Copy(&documentItem, &item)
		if documentItem.CountTax == "N" {
			documentItem.TaxAmount = decimal.Zero
		}
		documentItem.ItemDate = xtype.NewNullDate(item.ItemDate) // 日期轉換
		documentItem.Seq = seq
		documentItems = append(documentItems, &documentItem)
		if particulars != "" {
			particulars = fmt.Sprintf("%s; %s", particulars, s.generateParticulars(documentItem, timeLocation))
		} else {
			particulars = s.generateParticulars(documentItem, timeLocation)
		}

		seq += 1
	}

	for _, item := range otherItem.Items {
		var documentItem model.DocumentItem
		_ = copier.Copy(&documentItem, &item)
		documentItem.ItemDate = xtype.NewNullDate(item.ItemDate) // 日期轉換
		documentItem.Seq = seq
		documentItems = append(documentItems, &documentItem)
		if particulars != "" {
			particulars = fmt.Sprintf("%s; %s", particulars, s.generateParticulars(documentItem, timeLocation))
		} else {
			particulars = s.generateParticulars(documentItem, timeLocation)
		}
		seq += 1
	}

	return documentItems, particulars
}

func (s *confirmationNoteService) generateParticulars(documentItem model.DocumentItem, timeLocation *time.Location) string {
	if documentItem.ItemType == model.DocumentItemTypeWages {
		t := time.Now().In(timeLocation) // 使用服務地點的時區
		tzStr := s.formatTimeZoneString(t, timeLocation)

		return fmt.Sprintf("%s, %s %s-%s %s, $%s/h, $%s",
			documentItem.Particular,
			now.MustParse(documentItem.ItemDate.String()).Format(xtool.DateDayC),
			documentItem.StartTime,
			documentItem.FinishTime,
			tzStr,
			s.CustomerHumanizeAmount(documentItem.HourlyRate, 2),
			s.CustomerHumanizeAmount(documentItem.TotalAmount, 2),
		)
	} else {
		return fmt.Sprintf("%s, $%s", documentItem.Particular, s.CustomerHumanizeAmount(documentItem.TotalAmount, 2))
	}
}
func (s *confirmationNoteService) formatTimeZoneString(t time.Time, loc *time.Location) string {
	t = t.In(loc)
	name, _ := t.Zone()

	return name
}

// region ---------------------------------------------------- 更新確認通知單 ----------------------------------------------------

type ConfirmationNoteUpdateReq struct {
	DocumentId           uint64                         `json:"documentId" binding:"required"`
	WagesItem            ConfirmationNoteItemSummaryReq `json:"wagesItem" binding:"required"`
	OtherItem            ConfirmationNoteItemSummaryReq `json:"otherItem" binding:"required"`
	Remark               string                         `json:"remark"`
	OtherRemark          string                         `json:"otherRemark"`
	WagesDocumentFileIds []uint64                       `json:"wagesDocumentFileIds"`
	OtherDocumentFileIds []uint64                       `json:"otherDocumentFileIds"`
	TotalAmount          decimal.Decimal                `json:"totalAmount"`
	TaxAmount            decimal.Decimal                `json:"taxAmount"`
	SuperAmount          decimal.Decimal                `json:"superAmount"`
	GrandTotal           decimal.Decimal                `json:"grandTotal"`
}

func (s *confirmationNoteService) Update(db *gorm.DB, document model.Document, req ConfirmationNoteUpdateReq, superRate decimal.Decimal, taxRate decimal.Decimal, jobApplication model.JobApplication, professional model.Professional) error {
	var err error

	// 查詢工作
	var job model.Job
	if err = db.First(&job, jobApplication.JobId).Error; err != nil {
		return err
	}

	// 查詢專業人士用戶信息
	var professionalUser xmodel.User
	if err = db.Where("id = ?", professional.UserId).
		First(&professionalUser).Error; err != nil {
		return err
	}
	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", document.FacilityId).
		First(&facilityProfile, job.FacilityProfileId).Error; err != nil {
		return err
	}
	var serviceLocation model.ServiceLocation
	if err = db.First(&serviceLocation, job.ServiceLocationId).Error; err != nil {
		return err
	}

	location, err := time.LoadLocation(serviceLocation.Timezone)
	if err != nil {
		return err
	}

	// 計算總金額和稅金
	documentItems, particulars := s.generateItems(req.WagesItem, req.OtherItem, location)

	// 計算佣金
	commissionAmount, err := s.CalculateCommissionAmount(db, document.DataType, documentItems, facilityProfile.FacilityId)
	if err != nil {
		return err
	}

	// 更新確認通知單
	currentTime := time.Now().UTC()
	document.Particular = particulars
	document.TotalAmount = req.TotalAmount
	document.TaxAmount = req.TaxAmount
	document.TaxRate = taxRate
	document.SuperRate = superRate
	document.SuperAmount = req.SuperAmount
	document.CommissionAmount = commissionAmount
	document.GrandTotal = req.GrandTotal
	document.UpdateTime = &currentTime
	document.UpdateUserId = professional.UserId
	document.Remark = req.Remark
	document.OtherRemark = req.OtherRemark

	if err = db.Save(&document).Error; err != nil {
		return err
	}

	if err = db.Delete(&model.DocumentItem{}, "document_id = ?", req.DocumentId).Error; err != nil {
		return err
	}

	// 存儲
	for _, documentItem := range documentItems {
		documentItem.DocumentId = document.Id
	}

	if err = db.Create(&documentItems).Error; err != nil {
		return err
	}

	// 更新項目
	if err = db.Delete(&model.DocumentFileRelation{}, "document_id = ?", req.DocumentId).Error; err != nil {
		return err
	}

	// 存儲附件
	for index, fileId := range req.WagesDocumentFileIds {
		db.Create(&model.DocumentFileRelation{
			DocumentId:     document.Id,
			DocumentFileId: fileId,
			Seq:            int32(index + 1),
		})
	}

	for index, fileId := range req.OtherDocumentFileIds {
		db.Create(&model.DocumentFileRelation{
			DocumentId:     document.Id,
			DocumentFileId: fileId,
			Seq:            int32(index + 1),
		})
	}

	return nil
}

// endregion ---------------------------------------------------- 更新確認通知單 ----------------------------------------------------

// region ---------------------------------------------------- 提交確認通知單 ----------------------------------------------------

type ConfirmationNoteSubmitReq struct {
	DocumentId            uint64                         `json:"documentId"`
	JobApplicationId      uint64                         `json:"jobApplicationId" binding:"required_without=DocumentId"`
	FromName              string                         `json:"fromName" binding:"required_without=DocumentId"`
	FromBankStateBranch   string                         `json:"fromBankStateBranch" binding:"required_without=DocumentId"`
	FromBankAccountNumber string                         `json:"fromBankAccountNumber" binding:"required_without=DocumentId"`
	FromBankAccountName   string                         `json:"fromBankAccountName" binding:"required_without=DocumentId"`
	ToName                string                         `json:"toName" binding:"required_without=DocumentId"`
	ToAddress             string                         `json:"toAddress" binding:"required_without=DocumentId"`
	ToAbn                 string                         `json:"toAbn" binding:"required_without=DocumentId"`
	WagesItem             ConfirmationNoteItemSummaryReq `json:"wagesItem" binding:"required"`
	OtherItem             ConfirmationNoteItemSummaryReq `json:"otherItem" binding:"required"`
	Remark                string                         `json:"remark"`
	OtherRemark           string                         `json:"otherRemark"`
	WagesDocumentFileIds  []uint64                       `json:"wagesDocumentFileIds"`
	OtherDocumentFileIds  []uint64                       `json:"otherDocumentFileIds"`
	TotalAmount           decimal.Decimal                `json:"totalAmount"`
	TaxAmount             decimal.Decimal                `json:"taxAmount"`
	SuperAmount           decimal.Decimal                `json:"superAmount"`
	GrandTotal            decimal.Decimal                `json:"grandTotal"`
}

func (s *confirmationNoteService) Submit(db *gorm.DB, document model.Document, req ConfirmationNoteSubmitReq, superRate decimal.Decimal, taxRate decimal.Decimal, jobApplication model.JobApplication, professional model.Professional) error {
	var err error
	var documentId uint64

	if document.Id > 0 {
		documentId = document.Id
		var updateReq ConfirmationNoteUpdateReq
		_ = copier.Copy(&updateReq, &req)

		err = s.Update(db, document, updateReq, document.SuperRate, document.TaxRate, jobApplication, professional)
		if err != nil {
			return err
		}
		// 更新狀態
		if err = db.Model(&model.Document{}).Where("id = ?", documentId).Updates(map[string]interface{}{
			"progress":       model.DocumentProgressSent,
			"update_time":    time.Now().UTC(),
			"update_user_id": professional.UserId,
		}).Error; err != nil {
			return err
		}
	} else {
		var createReq ConfirmationNoteCreateReq
		_ = copier.Copy(&createReq, &req)

		resp, err := s.Create(db, createReq, superRate, taxRate, jobApplication, professional)
		if err != nil {
			return err
		}
		documentId = resp.DocumentId
		// 更新狀態
		if err = db.Model(&model.Document{}).Where("id = ?", documentId).Updates(map[string]interface{}{
			"progress":       model.DocumentProgressSent,
			"update_time":    time.Now().UTC(),
			"update_user_id": professional.UserId,
		}).Error; err != nil {
			return err
		}

		// Facility收到新的確認單，需要去審核 - 通知Facility
		var createdDocument model.Document
		if err = db.First(&createdDocument, resp.DocumentId).Error; err != nil {
			return err
		}

		// 發送確認單接收通知給機構
		if err = SystemNotificationService.CreateFacilityBillingConfirmationNoteReceived(db, CreateFacilityBillingConfirmationNoteReceivedReq{
			JobApplicationId:   req.JobApplicationId,
			ConfirmationNoteId: createdDocument.Id,
			GrandTotal:         createdDocument.GrandTotal,
		}); err != nil {
			return err
		}
	}

	// 檢查是否 賠付的confirmation note
	if document.DataType == model.DocumentDataTypeSystemCompensation {
		err = s.FacilityReview(db, FacilityConfirmationNoteReviewReq{
			DocumentId:     documentId,
			ApprovedUserId: 0,
			Action:         "CONFIRM",
			RejectReason:   "",
		})
		if err != nil {
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- 提交確認通知單 ----------------------------------------------------

// region ---------------------------------------------------- 作廢確認通知單 ----------------------------------------------------

type ConfirmationNoteCancelReq struct {
	DocumentId uint64 `json:"documentId" binding:"required"`
}

func (s *confirmationNoteService) Cancel(db *gorm.DB, req ConfirmationNoteCancelReq, category string, userId uint64) error {
	var err error

	// 查詢確認通知單
	var document model.Document
	if err = db.
		Where("category = ?", category).
		Where("user_id = ?", userId).
		First(&document, req.DocumentId).Error; err != nil {
		return err
	}

	// 更新狀態
	if err = db.Model(&model.Document{}).Where("id = ?", document.Id).Updates(map[string]interface{}{
		"progress":       model.DocumentProgressCancel,
		"update_time":    time.Now().UTC(),
		"update_user_id": userId,
	}).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 作廢確認通知單 ----------------------------------------------------

// region ---------------------------------------------------- 審批確認通知單 ----------------------------------------------------

type FacilityConfirmationNoteReviewReq struct {
	DocumentId     uint64 `json:"documentId" binding:"required"`
	Action         string `json:"action" binding:"required,oneof=CONFIRM REJECT"`
	RejectReason   string `json:"rejectReason" binding:"required_if=Action REJECT"`
	ApprovedUserId uint64 `json:"-"`
}

func (s *confirmationNoteService) FacilityReview(db *gorm.DB, req FacilityConfirmationNoteReviewReq) error {
	var err error

	if req.Action == model.DocumentProgressConfirm {
		var platformProfile PlatformProfile
		platformProfile, err = CommonSettingService.GetPlatformProfile(db)
		if err != nil {
			return err
		}

		// 複製到發票
		var document model.Document
		if err = db.Where("category = ?", model.DocumentCategoryConfirmation).Where("id = ?", req.DocumentId).First(&document).Error; err != nil {
			return err
		}
		var documentRelations []model.DocumentFileRelation
		if err = db.Where("document_id = ?", document.Id).Find(&documentRelations).Error; err != nil {
			return err
		}

		var documentItems []model.DocumentItem
		if err = db.Where("document_id = ?", document.Id).Find(&documentItems).Error; err != nil {
			return err
		}

		var professional model.Professional
		if err = db.First(&professional, document.ProfessionalId).Error; err != nil {
			return err
		}
		companyAbn := ProfessionalProfileService.CheckIsCompanyAbn(professional)

		originDocumentDate := document.DocumentDate.String()
		originDocumentNo := document.DocumentNo
		originSuperAmount := document.SuperAmount
		originCommissionAmount := document.CommissionAmount
		// 複製到 Invoice
		currentTime := time.Now()
		// 重置或修改數據
		document.FromDocumentId = document.Id
		document.Id = 0                                                           // 清空
		document.DocumentDate = xtype.NewDate(currentTime.Format(xtool.DateDayA)) // 更新日期
		document.DueDate = xtype.NewNullDate(currentTime.Add(7 * 24 * time.Hour).Format(xtool.DateDayA))
		document.Progress = model.DocumentProgressConfirm
		document.Category = model.DocumentCategoryInvoice
		document.PaymentReceived = model.DocumentProgressPaymentReceivedN
		document.CreateTime = currentTime.UTC().Truncate(time.Second)
		document.CreateUserId = document.UserId
		document.UpdateTime = nil
		document.UpdateUserId = 0
		document.Paid = model.DocumentPaidN

		invoiceToProfile := InvoiceToProfile{
			ToName:    document.ToName,
			ToAbn:     document.ToAbn,
			ToEmail:   document.ToEmail,
			ToAddress: document.ToAddress,
		}

		if document.DataType == model.DocumentDataTypeProfessionalToSystem {
			// 專業人員開立給系統的Confirm Note, 所以發票的 To 信息是 System 的信息
			document.ToName = platformProfile.PlatformName
			document.ToEmail = platformProfile.PlatformEmail
			document.ToAddress = platformProfile.PlatformAddress
			document.ToAbn = platformProfile.PlatformAbn
		}

		// 查詢工作
		var jobApplication model.JobApplication
		if err = db.First(&jobApplication, document.JobApplicationId).Error; err != nil {
			return err
		}
		var job model.Job
		if err = db.First(&job, jobApplication.JobId).Error; err != nil {
			return err
		}
		var serviceLocation model.ServiceLocation
		if err = db.First(&serviceLocation, job.ServiceLocationId).Error; err != nil {
			return err
		}
		location, err := time.LoadLocation(serviceLocation.Timezone)
		if err != nil {
			return err
		}

		systemToFacilityInvoiceDocumentItems := make([]model.DocumentItem, 0)
		systemToFacilityInvoiceCommissionAmount := decimal.Zero

		invoiceDocumentItems := make([]model.DocumentItem, 0)
		systemToFacilityInvoiceSuperAmount := decimal.Zero
		for _, item := range documentItems {
			s.formatInvoiceDocumentItemParticular(&item, location)
			// 根據 dataType 決定Invoice 中的 Item 項目
			if document.DataType == model.DocumentDataTypeProfessionalToFacility {
				item.CommissionAmount = decimal.Zero
				item.CommissionRate = decimal.Zero
				item.SuperAmount = decimal.Zero
				invoiceDocumentItems = append(invoiceDocumentItems, item)
			} else if document.DataType == model.DocumentDataTypeProfessionalToSystem {
				if item.ItemType == model.DocumentItemTypeAdditionalWages {
					// 額外薪金的 CommissionAmount 計算入 systemToFacilityInvoice 中
					systemToFacilityInvoiceCommissionAmount = systemToFacilityInvoiceCommissionAmount.Add(item.CommissionAmount)
				}
				// 系統開立給機構的發票 - 額外薪金+報銷+Super(額外薪金部分) 的 Item
				if item.ItemType != model.DocumentItemTypeWages {
					// 額外薪金+報銷+Super(額外薪金部分) 項目計算入 systemToFacilityInvoice 中 ,super(額外薪金部分) 項目下邊處理
					systemToFacilityInvoiceDocumentItems = append(systemToFacilityInvoiceDocumentItems, model.DocumentItem{
						ItemDate:    item.ItemDate,
						ItemName:    item.ItemName,
						ItemType:    item.ItemType,
						Particular:  item.Particular,
						TaxAmount:   item.TaxAmount,
						TotalAmount: item.TotalAmount,
						CountTax:    item.CountTax,
					})
					if item.ItemType == model.DocumentItemTypeAdditionalWages {
						systemToFacilityInvoiceSuperAmount = systemToFacilityInvoiceSuperAmount.Add(item.TotalAmount.Mul(document.SuperRate))
					}
				}
				item.CommissionAmount = decimal.Zero
				item.CommissionRate = decimal.Zero
				item.SuperAmount = decimal.Zero
				invoiceDocumentItems = append(invoiceDocumentItems, item)
			} else if document.DataType == model.DocumentDataTypeSystemCompensation {
				item.CommissionAmount = decimal.Zero
				item.CommissionRate = decimal.Zero
				item.SuperAmount = decimal.Zero
				invoiceDocumentItems = append(invoiceDocumentItems, item)
			}
		}

		if document.DataType == model.DocumentDataTypeProfessionalToFacility {
			systemToFacilityInvoiceCommissionAmount = originCommissionAmount
		}

		if systemToFacilityInvoiceCommissionAmount.GreaterThan(decimal.Zero) {
			systemToFacilityInvoiceDocumentItems = append([]model.DocumentItem{
				{
					ItemType:    model.DocumentItemTypeCommissionAmount,
					ItemDate:    xtype.NewNullDate(originDocumentDate),
					Particular:  fmt.Sprintf("%s (%s)", documentItemTypeStringMap[model.DocumentItemTypeCommissionAmount], originDocumentNo.String),
					TotalAmount: systemToFacilityInvoiceCommissionAmount,
					CountTax:    "N",
				},
			}, systemToFacilityInvoiceDocumentItems...)
		}

		if document.DataType == model.DocumentDataTypeProfessionalToFacility {
			if companyAbn {
				// companyAbn - 專業人員開立給機構的發票 - Super 的 Item
				if originSuperAmount.GreaterThan(decimal.Zero) {
					invoiceDocumentItems = append(invoiceDocumentItems, model.DocumentItem{
						ItemType:    model.DocumentItemTypeSuperAmount,
						Particular:  fmt.Sprintf("%s (%s)", documentItemTypeStringMap[model.DocumentItemTypeSuperAmount], originDocumentNo.String),
						TotalAmount: originSuperAmount,
						CountTax:    "N",
					})
				}
			} else {
				// Sole Trader - 系統開立給機構的發票 - Super 的 Item
				if originSuperAmount.GreaterThan(decimal.Zero) {
					systemToFacilityInvoiceDocumentItems = append(systemToFacilityInvoiceDocumentItems, model.DocumentItem{
						ItemType:    model.DocumentItemTypeSuperAmount,
						Particular:  fmt.Sprintf("%s (%s)", documentItemTypeStringMap[model.DocumentItemTypeSuperAmount], originDocumentNo.String),
						TotalAmount: originSuperAmount,
						CountTax:    "N",
					})
				}
			}
		} else if document.DataType == model.DocumentDataTypeProfessionalToSystem {
			// 系統開立給機構的發票 - Super(額外薪金部分) 的 Item
			if systemToFacilityInvoiceSuperAmount.GreaterThan(decimal.Zero) {
				systemToFacilityInvoiceDocumentItems = append(systemToFacilityInvoiceDocumentItems, model.DocumentItem{
					ItemType:    model.DocumentItemTypeSuperAmount,
					Particular:  fmt.Sprintf("%s (%s)", documentItemTypeStringMap[model.DocumentItemTypeSuperAmount], originDocumentNo.String),
					TotalAmount: systemToFacilityInvoiceSuperAmount,
					CountTax:    "N",
				})
			}
		}

		if document.DataType == model.DocumentDataTypeSystemCompensation {
			// 賠付賬單, 生成 invoice 應該是 P2F
			document.DataType = model.DocumentDataTypeProfessionalToFacility
		}

		particular, totalAmount, taxAmount := s.generateInvoiceParticularAndAmount(invoiceDocumentItems)
		document.Particular = particular
		document.TotalAmount = totalAmount
		document.TaxAmount = taxAmount
		document.SuperAmount = decimal.Zero      // 重置金額
		document.CommissionAmount = decimal.Zero // 重置金額
		document.GrandTotal = document.TotalAmount.Add(document.TaxAmount)
		if err = db.Create(&document).Error; err != nil {
			return err
		}

		// 保存 invoiceDocumentItems 的數據
		for i, item := range invoiceDocumentItems {
			item.Id = 0
			item.DocumentId = document.Id
			item.Seq = int32(i + 1)
			if err = db.Create(&item).Error; err != nil {
				return err
			}
		}

		for _, relation := range documentRelations {
			relation.Id = 0
			relation.DocumentId = document.Id
			if err = db.Create(&relation).Error; err != nil {
				return err
			}
		}

		// 更新確認通知單狀態
		if err = db.Model(&model.Document{}).Where("id = ?", req.DocumentId).Updates(map[string]interface{}{
			"progress":    model.DocumentProgressConfirm,
			"update_time": currentTime.UTC(),
		}).Error; err != nil {
			return err
		}

		// facility通過confirmation note - 通知Professional
		// 發送確認單通過通知給專業人士
		grandTotalFloat, _ := document.GrandTotal.Float64()
		if err = SystemNotificationService.CreateProfessionalBillingConfirmationNoteApproved(db, CreateProfessionalBillingConfirmationNoteApprovedReq{
			UserId:             jobApplication.ProfessionalId,
			JobId:              jobApplication.JobId,
			FacilityId:         job.FacilityId,
			ConfirmationNoteId: req.DocumentId,
			InvoiceId:          document.Id,
			Amount:             grandTotalFloat,
			CreatorUserId:      req.ApprovedUserId,
		}); err != nil {
			return err
		}

		if len(systemToFacilityInvoiceDocumentItems) > 0 {
			// 生成 systemToFacilityInvoice 的 invoice
			err = s.CreateSystemToFacilityInvoice(db, document, systemToFacilityInvoiceDocumentItems, platformProfile, invoiceToProfile)
			if err != nil {
				return err
			}
		}
	} else if req.Action == model.DocumentProgressReject {
		if err = db.Model(&model.Document{}).Where("id = ?", req.DocumentId).Updates(map[string]interface{}{
			"progress":      req.Action,
			"reject_reason": req.RejectReason,
		}).Error; xgorm.IsSqlErr(err) {
			return err
		}

		// facility拒絕confirmation note - 通知Professional
		// 發送確認單拒絕通知給專業人士
		if err = SystemNotificationService.CreateProfessionalBillingConfirmationNoteRejected(db, CreateProfessionalBillingConfirmationNoteRejectedReq{
			RejectionReason:    req.RejectReason,
			CreatorUserId:      req.ApprovedUserId,
			ConfirmationNoteId: req.DocumentId,
		}); err != nil {
			return err
		}
	} else {
		return errors.New("invalid action")
	}

	return nil
}

func (s *confirmationNoteService) formatInvoiceDocumentItemParticular(documentItem *model.DocumentItem, timeLocation *time.Location) {
	if documentItem.ItemType == model.DocumentItemTypeWages {
		t := time.Now().In(timeLocation) // 使用服務地點的時區
		tzStr := s.formatTimeZoneString(t, timeLocation)

		documentItem.Particular = fmt.Sprintf("%s, %s %s-%s %s, $%s/h, $%s",
			documentItem.Particular,
			now.MustParse(documentItem.ItemDate.String()).Format(xtool.DateDayC),
			documentItem.StartTime,
			documentItem.FinishTime,
			tzStr,
			s.CustomerHumanizeAmount(documentItem.HourlyRate, 2),
			s.CustomerHumanizeAmount(documentItem.TotalAmount, 2),
		)
	}
	if documentItem.CountTax == "N" {
		documentItem.TaxAmount = decimal.Zero
	}
	itemName, exist := documentItemTypeStringMap[documentItem.ItemType]
	if exist {
		documentItem.Particular = fmt.Sprintf("%s (%s)", itemName, documentItem.Particular)
	} else {
		documentItem.Particular = fmt.Sprintf("%s (%s)", documentItem.ItemName, documentItem.Particular)
	}
}

func (s *confirmationNoteService) generateInvoiceParticularAndAmount(documentItems []model.DocumentItem) (string, decimal.Decimal, decimal.Decimal) {
	particular := ""
	totalAmount := decimal.Zero
	taxAmount := decimal.Zero

	for _, item := range documentItems {
		if particular != "" {
			particular = fmt.Sprintf("%s; %s", particular, item.Particular)
		} else {
			particular = fmt.Sprintf("%s", item.Particular)
		}
		totalAmount = totalAmount.Add(item.TotalAmount)
		if item.CountTax == "Y" {
			taxAmount = taxAmount.Add(item.TaxAmount)
		}
	}

	return particular, totalAmount, taxAmount
}

type InvoiceToProfile struct {
	ToName    string `json:"toName"`    // TO 名稱
	ToAbn     string `json:"toAbn"`     // TO ABN (澳洲商業號碼)
	ToEmail   string `json:"toEmail"`   // TO 郵件
	ToAddress string `json:"toAddress"` // TO 地址
}

func (s *confirmationNoteService) CreateSystemToFacilityInvoice(db *gorm.DB, document model.Document, documentItems []model.DocumentItem, platformProfile PlatformProfile, invoiceToProfile InvoiceToProfile) error {
	nowTime := time.Now().UTC().Truncate(time.Second)
	var err error
	var systemToFacilityInvoice model.Document
	_ = copier.Copy(&systemToFacilityInvoice, &document)
	systemToFacilityInvoice.Id = 0
	systemToFacilityInvoice.DataType = model.DocumentDataTypeSystemToFacility
	documentNo, seqNo, err := InvoiceService.GenerateDocumentNo(db, systemToFacilityInvoice.Category, systemToFacilityInvoice.DataType, nowTime)
	if err != nil {
		return err
	}

	systemToFacilityInvoice.DocumentNo = xtype.NotNullString(documentNo)
	systemToFacilityInvoice.SeqNo = seqNo
	systemToFacilityInvoice.ProfessionalId = 0
	systemToFacilityInvoice.UserId = 0
	// Medic Crew 的信息
	systemToFacilityInvoice.FromName = platformProfile.PlatformName
	systemToFacilityInvoice.FromEmail = platformProfile.PlatformEmail
	systemToFacilityInvoice.FromAddress = platformProfile.PlatformAddress
	systemToFacilityInvoice.FromBankStateBranch = platformProfile.PlatformBankStateBranch
	systemToFacilityInvoice.FromBankAccountNumber = platformProfile.PlatformBankAccountNumber
	systemToFacilityInvoice.FromBankAccountName = platformProfile.PlatformBankAccountName
	systemToFacilityInvoice.FromAbn = platformProfile.PlatformAbn

	systemToFacilityInvoice.ToName = invoiceToProfile.ToName
	systemToFacilityInvoice.ToAbn = invoiceToProfile.ToAbn
	systemToFacilityInvoice.ToEmail = invoiceToProfile.ToEmail
	systemToFacilityInvoice.ToAddress = invoiceToProfile.ToAddress

	particular, totalAmount, taxAmount := s.generateInvoiceParticularAndAmount(documentItems)
	systemToFacilityInvoice.Particular = particular
	systemToFacilityInvoice.TotalAmount = totalAmount
	systemToFacilityInvoice.TaxAmount = taxAmount
	systemToFacilityInvoice.CommissionAmount = decimal.Zero
	systemToFacilityInvoice.GrandTotal = systemToFacilityInvoice.TotalAmount.Add(systemToFacilityInvoice.TaxAmount)
	systemToFacilityInvoice.PaymentReceived = model.DocumentProgressPaymentReceivedN
	systemToFacilityInvoice.CreateTime = nowTime
	systemToFacilityInvoice.Remark = ""
	systemToFacilityInvoice.OtherRemark = ""
	document.Paid = model.DocumentPaidN
	if err = db.Create(&systemToFacilityInvoice).Error; err != nil {
		return err
	}

	// 保存 documentItems 的數據
	for i, item := range documentItems {
		item.Id = 0
		item.DocumentId = systemToFacilityInvoice.Id
		item.Seq = int32(i + 1)
		if err = db.Create(&item).Error; err != nil {
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- 審批確認通知單 ----------------------------------------------------

func (s *confirmationNoteService) CustomerHumanizeAmount(amount decimal.Decimal, decimals int) string {
	amountFloat, _ := amount.Float64()
	str := humanize.CommafWithDigits(amountFloat, decimals)
	// 檢查有沒有小數點
	if decimals > 0 {
		if !strings.Contains(str, ".") {
			// 沒有小數點，直接補
			str = str + "." + strings.Repeat("0", decimals)
		} else {
			parts := strings.Split(str, ".")
			need := decimals - len(parts[1])
			if need > 0 {
				str = str + strings.Repeat("0", need)
			}
		}
	}
	return str
}
