package services

import (
	"errors"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xhermes"
	"github.com/Norray/xrocket/xi18n"
	"github.com/Norray/xrocket/xmail"
	"github.com/Norray/xrocket/xmodel"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xredis"
	"github.com/Norray/xrocket/xtool"
	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/matcornic/hermes/v2"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	uuid "github.com/satori/go.uuid"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
)

const (
	RegisterUserTypeFacility      = "FACILITY"
	RegisterUserTypeProfessional  = "PROFESSIONAL"
	facilityRegisterKeyPrefix     = "facility_register:%s"
	professionalRegisterKeyPrefix = "professional_register:%s"
	registerRecordExpiredTime     = 30 * time.Minute // 註冊記錄過期時間
	registerCodeExpiredTime       = 10 * time.Minute // 註冊驗證碼緩存時間
	referenceFormExpiredTime      = 10 * time.Minute // 推薦人填表時驗證碼緩存時間
	expiredOffset                 = 10 * time.Second // 過期偏移時間 （結束前10秒不再接受）
	resendCountLimit              = 5                // 重發驗證碼次數限制
)

var RegisterService = new(registerService)

type registerService struct{}

func (s *registerService) GetRegisterInfoKey(userType string, uuid string) string {
	switch userType {
	case RegisterUserTypeFacility:
		return fmt.Sprintf(facilityRegisterKeyPrefix, uuid)
	case RegisterUserTypeProfessional:
		return fmt.Sprintf(professionalRegisterKeyPrefix, uuid)
	}
	// 不能觸發，必須在switch中處理
	panic(fmt.Sprintf("Invalid user type: %s", userType))
}

// region ---------------------------------------------------- Checker ----------------------------------------------------

// 檢查用戶郵箱是否唯一
func (s *registerService) CheckUserEmailUnique(db *gorm.DB, email string, userId ...uint64) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.register.user.email.already_exists",
		Other: "Email already registered. Please try another email.",
	}
	var count int64
	builder := db.Model(&xmodel.User{}).Where("email = ?", email)
	if len(userId) > 0 {
		builder = builder.Where("id != ?", userId[0])
	}
	if err := builder.Count(&count).Error; err != nil {
		return false, msg, err
	}
	if count > 0 {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 發送註冊驗證碼 ----------------------------------------------------

type SendRegisterVerificationCodeReq struct {
	UserType       string `json:"userType" binding:"required,oneof=FACILITY PROFESSIONAL"` // 用戶類型 FACILITY=機構，PROFESSIONAL=專業人士
	Email          string `json:"email" binding:"required,email"`                          // 電郵
	RecaptchaToken string `json:"recaptchaToken" binding:"required"`                       // Recaptcha Token
}

type SendRegisterVerificationCodeResp struct {
	Uuid string `json:"uuid"`
}

// 發送激活碼電郵
func (s *registerService) sendMailCode(code string, email string, lang string) error {
	var err error
	emailGreeting := i18n.Message{
		ID:    "register.email.greeting",
		Other: "Hi",
	}
	emailSignature := i18n.Message{
		ID:    "register.email.signature",
		Other: "Thank you",
	}
	emailIntros := i18n.Message{
		ID:    "register.email.intros",
		Other: "Thank you for registering as a Medic Crew user.",
	}
	emailInstructions := i18n.Message{
		ID:    "register.email.instructions",
		Other: "Below is your registration activation code. Please enter it within 10 minutes.",
	}
	emailSubject := i18n.Message{
		ID:    "register.email.subject",
		Other: "Registration Verification Code",
	}
	// 構建電郵內容
	body := hermes.Body{
		Greeting:  xi18n.LocalizeWithLang(lang, &emailGreeting),
		Name:      "",
		Signature: xi18n.LocalizeWithLang(lang, &emailSignature),
		Intros: []string{
			xi18n.LocalizeWithLang(lang, &emailIntros),
		},
		Actions: []hermes.Action{
			{
				Instructions: xi18n.LocalizeWithLang(lang, &emailInstructions),
				InviteCode:   code,
			},
		},
	}
	content, err := xhermes.GenerateHTML(HermesDefaultProduct(medicCrewLogoUrl), body)
	if err != nil {
		return err
	}

	err = xmail.SendMail(email, fmt.Sprintf("[%s] %s", xconfig.MailConf.Name, xi18n.LocalizeWithLang(lang, &emailSubject)), "text/html", content)
	if err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 發送註冊驗證碼 ----------------------------------------------------

// region ---------------------------------------------------- 校驗註冊驗證碼 ----------------------------------------------------

func (s *registerService) CheckerRegisterVerificationCode(registerUserType string, registerInfo interface{}, code string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.register.verification.code.invalid",
		Other: "Invalid verification code.",
	}

	var verifyInfo RegisterVerifyInfo
	switch registerUserType {
	case RegisterUserTypeFacility:
		verifyInfo = registerInfo.(RegisterFacilityInfo).RegisterVerifyInfo
	case RegisterUserTypeProfessional:
		verifyInfo = registerInfo.(RegisterProfessionalInfo).RegisterVerifyInfo
	default:
		panic(fmt.Sprintf("Invalid register user type: %s", registerUserType))
	}

	if verifyInfo.CodeExpiredTime < time.Now().Unix() {
		return false, msg, nil
	}

	if verifyInfo.Code != code {
		return false, msg, nil
	}
	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 校驗註冊驗證碼 ----------------------------------------------------

// region ---------------------------------------------------- 檢查是否有相關註冊信息 ----------------------------------------------------

func (s *registerService) CheckerRegisterInfoExists(c *gin.Context, registerInfo interface{}, registerUserType string, uuid string) (bool, i18n.Message, error) {
	var err error
	msg := i18n.Message{
		ID:    "checker.register.info.not_exists",
		Other: "The registration request has timed out. Please register again.",
	}

	key := s.GetRegisterInfoKey(registerUserType, uuid)
	var exist bool
	exist, err = xredis.GetStruct(c, key, &registerInfo)

	if err != nil {
		return false, msg, err
	}
	if !exist {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 校驗註冊驗證碼 ----------------------------------------------------

// region ---------------------------------------------------- 檢查註冊驗證碼是否過期 ----------------------------------------------------

func (s *registerService) CheckerRegisterCodeExpired(registerUserType string, registerInfo interface{}) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.register.code.expired",
		Other: "The verification code has expired. Please resend the verification code.",
	}
	var verifyInfo RegisterVerifyInfo
	switch registerUserType {
	case RegisterUserTypeFacility:
		verifyInfo = registerInfo.(RegisterFacilityInfo).RegisterVerifyInfo
	case RegisterUserTypeProfessional:
		verifyInfo = registerInfo.(RegisterProfessionalInfo).RegisterVerifyInfo
	}

	if verifyInfo.CodeExpiredTime < time.Now().Unix() {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 校驗註冊驗證碼 ----------------------------------------------------

// region ---------------------------------------------------- 檢查註冊驗證碼發送次數 ----------------------------------------------------

func (s *registerService) CheckerRegisterCodeSendCount(registerUserType string, registerInfo interface{}) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.register.code.send.count.limit",
		Other: "The registration request has timed out. Please register again.",
	}
	var verifyInfo RegisterVerifyInfo
	switch registerUserType {
	case RegisterUserTypeFacility:
		verifyInfo = registerInfo.(RegisterFacilityInfo).RegisterVerifyInfo
	case RegisterUserTypeProfessional:
		verifyInfo = registerInfo.(RegisterProfessionalInfo).RegisterVerifyInfo
	}

	if verifyInfo.SendCount >= resendCountLimit {
		return false, msg, nil
	}

	return true, i18n.Message{}, nil
}

// endregion ---------------------------------------------------- 校驗註冊驗證碼 ----------------------------------------------------

// region ---------------------------------------------------- 機構註冊 ----------------------------------------------------

type RegisterVerifyInfo struct {
	Uuid              string `json:"uuid"`              // 電郵驗證碼UUID
	Email             string `json:"email"`             // 電郵
	Code              string `json:"code"`              // 電郵驗證碼
	CodeExpiredTime   int64  `json:"codeExpiredTime"`   // 電郵驗證碼過期時間(時間戳)
	RecordExpiredTime int64  `json:"recordExpiredTime"` // 註冊記錄過期時間(時間戳)
	SendCount         int    `json:"sendCount"`         // 發送了多少次
}

// 機構註冊信息緩存
type RegisterFacilityInfo struct {
	Password string `json:"password"` // 密碼
	RegisterVerifyInfo
}

func (r *RegisterVerifyInfo) GenerateCode() error {
	var err error
	r.Code, err = xtool.GenRandIntWithZeroDigit(999999, 6)
	if err != nil {
		return err
	}
	r.CodeExpiredTime = time.Now().Add(registerCodeExpiredTime - expiredOffset).Unix()
	r.RecordExpiredTime = time.Now().Add(registerRecordExpiredTime - expiredOffset).Unix()
	return nil
}

// 專業人士註冊信息緩存
type RegisterProfessionalInfo struct {
	Password      string `json:"password"` // 密碼
	OAuthPlatform string `json:"oauthPlatform"`
	OAuthId       string `json:"oauthId"`
	RegisterVerifyInfo
}

type RegisterFacilityReq struct {
	Email          string `json:"email" binding:"required,email"`    // 電郵
	Password       string `json:"password" binding:"required"`       // 密碼
	RecaptchaToken string `json:"recaptchaToken" binding:"required"` // Recaptcha Token
}

type RegisterFacilityResp struct {
	EmailVerifyUuid   string `json:"emailVerifyUuid"`   // 電郵驗證碼UUID
	CodeExpiredTime   int64  `json:"codeExpiredTime"`   // 電郵驗證碼過期時間(時間戳)
	RecordExpiredTime int64  `json:"recordExpiredTime"` // 註冊記錄過期時間(時間戳)
}

func (s *registerService) RegisterFacility(c *gin.Context, req RegisterFacilityReq) (RegisterFacilityResp, error) {
	var err error
	var resp RegisterFacilityResp

	cacheInfo := RegisterFacilityInfo{
		Password: req.Password,
		RegisterVerifyInfo: RegisterVerifyInfo{
			Email:     req.Email,
			Uuid:      uuid.NewV4().String() + uuid.NewV4().String(),
			SendCount: 1,
		},
	}
	if err = cacheInfo.GenerateCode(); err != nil {
		return resp, err
	}

	// 緩存驗證碼
	key := s.GetRegisterInfoKey(RegisterUserTypeFacility, cacheInfo.Uuid)
	err = xredis.SetStruct(c, key, cacheInfo, registerRecordExpiredTime)
	if err != nil {
		return resp, err
	}

	// 發送電郵
	nc := xapp.NGinCtx{C: c}
	lang := nc.GetLanguage()
	err = s.sendMailCode(cacheInfo.Code, req.Email, lang)
	if err != nil {
		return resp, err
	}

	resp.EmailVerifyUuid = cacheInfo.Uuid
	resp.CodeExpiredTime = cacheInfo.CodeExpiredTime
	resp.RecordExpiredTime = cacheInfo.RecordExpiredTime
	return resp, nil
}

// endregion ---------------------------------------------------- 機構註冊 ----------------------------------------------------

// region ---------------------------------------------------- 機構驗證電郵確認註冊 ----------------------------------------------------

type RegisterVerifyFacilityEmailReq struct {
	EmailVerifyUuid string `json:"emailVerifyUuid" binding:"required"` // 電郵驗證碼UUID
	EmailVerifyCode string `json:"emailVerifyCode" binding:"required"` // 電郵驗證碼
}

func (s *registerService) RegisterVerifyFacilityEmail(db *gorm.DB, registerFacilityInfo RegisterFacilityInfo) error {
	var err error

	// 創建用戶
	var user xmodel.User
	user.Name = registerFacilityInfo.Email
	user.Username = registerFacilityInfo.Email
	user.Email = registerFacilityInfo.Email
	if registerFacilityInfo.Password != "" {
		user.Password = xtool.EncodeStringWithSalt(registerFacilityInfo.Password, xconfig.AppConf.PasswordSalt)
	}
	user.UserType = model.UserUserTypeFacilityUser
	user.Status = xmodel.UserStatusEnable
	if err = db.Create(&user).Error; err != nil {
		return err
	}

	// 綁定角色
	var role xmodel.Role
	if err = db.
		Where("user_type = ?", model.UserUserTypeFacilityUser).
		Where("role_code = ?", model.UserUserTypeFacilityUser).
		First(&role).Error; err != nil {
		return err
	}
	userRole := xmodel.UserRole{
		UserId: user.Id,
		RoleId: role.Id,
	}
	if err = db.Create(&userRole).Error; err != nil {
		return err
	}

	// 創建機構
	var facility model.Facility
	facility.Deactivated = model.FacilityDeactivatedN
	if err = db.Create(&facility).Error; err != nil {
		return err
	}

	// 綁定機構角色
	facilityRole := model.FacilityRole{
		FacilityId: facility.Id,
		RoleId:     role.Id,
	}
	if err = db.Create(&facilityRole).Error; err != nil {
		return err
	}

	facilityUser := model.FacilityUser{
		FacilityId:  facility.Id,
		UserId:      user.Id,
		PrimaryUser: model.FacilityUserPrimaryUserY,
	}
	if err = db.Create(&facilityUser).Error; err != nil {
		return err
	}
	facilityProfile := model.FacilityProfile{
		FacilityId:                         facility.Id,
		DataType:                           model.FacilityProfileDataTypeDraft,
		Status:                             model.FacilityProfileStatusPending,
		ApprovedTime:                       nil,
		Email:                              registerFacilityInfo.Email,
		PublicLiabilityInsuranceExpiryDate: xtype.NewNullDate(),
	}
	if err = db.Create(&facilityProfile).Error; err != nil {
		return err
	}

	// 初始化時薪設定
	if err = HourlyRateService.Init(db, facility.Id); err != nil {
		return err
	}

	// 初始化福利數據
	if err = BenefitService.Init(db, facility.Id); err != nil {
		return err
	}

	// 初始化津貼數據
	if err = AllowanceService.Init(db, facility.Id); err != nil {
		return err
	}

	// TODO: 機構註冊完成歡迎通知 - 通知Facility
	// 發送歡迎通知給新註冊的機構
	// 通知內容：歡迎加入Medic Crew！請完善您的機構資料以開始發布工作職位

	return nil
}

// endregion ---------------------------------------------------- 機構驗證電郵確認註冊 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士註冊 ----------------------------------------------------

type RegisterProfessionalReq struct {
	Email          string `json:"email" binding:"required,email"`    // 電郵
	Password       string `json:"password" binding:"required"`       // 密碼
	RecaptchaToken string `json:"recaptchaToken" binding:"required"` // Recaptcha Token
}

type RegisterProfessionalResp struct {
	EmailVerifyUuid   string `json:"emailVerifyUuid"`   // 電郵驗證碼UUID
	CodeExpiredTime   int64  `json:"codeExpiredTime"`   // 電郵驗證碼過期時間(時間戳)
	RecordExpiredTime int64  `json:"recordExpiredTime"` // 註冊記錄過期時間(時間戳)
}

func (s *registerService) RegisterProfessional(c *gin.Context, req RegisterProfessionalReq) (RegisterProfessionalResp, error) {
	var err error
	var resp RegisterProfessionalResp
	cacheInfo := RegisterProfessionalInfo{
		Password: req.Password,
		RegisterVerifyInfo: RegisterVerifyInfo{
			Email:     req.Email,
			Uuid:      uuid.NewV4().String() + uuid.NewV4().String(),
			SendCount: 1,
		},
	}

	if err = cacheInfo.GenerateCode(); err != nil {
		return resp, err
	}
	// 緩存驗證碼
	key := s.GetRegisterInfoKey(RegisterUserTypeProfessional, cacheInfo.Uuid)
	err = xredis.SetStruct(c, key, cacheInfo, registerRecordExpiredTime)
	if err != nil {
		return resp, err
	}

	// 發送電郵
	nc := xapp.NGinCtx{C: c}
	lang := nc.GetLanguage()
	err = s.sendMailCode(cacheInfo.Code, req.Email, lang)
	if err != nil {
		return resp, err
	}

	resp.EmailVerifyUuid = cacheInfo.Uuid
	resp.CodeExpiredTime = cacheInfo.CodeExpiredTime
	resp.RecordExpiredTime = cacheInfo.RecordExpiredTime
	return resp, nil
}

// endregion ---------------------------------------------------- 專業人士註冊 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士驗證電郵確認註冊 ----------------------------------------------------

type RegisterVerifyProfessionalEmailReq struct {
	EmailVerifyUuid string `json:"emailVerifyUuid" binding:"required"` // 電郵驗證碼UUID
	EmailVerifyCode string `json:"emailVerifyCode" binding:"required"` // 電郵驗證碼
}

func (s *registerService) RegisterVerifyProfessionalEmail(db *gorm.DB, registerProfessionalInfo RegisterProfessionalInfo) (xmodel.User, error) {
	var err error

	// 創建用戶
	user := xmodel.User{
		Name:          registerProfessionalInfo.Email,
		Username:      registerProfessionalInfo.Email,
		Email:         registerProfessionalInfo.Email,
		OAuthId:       registerProfessionalInfo.OAuthId,
		OAuthPlatform: registerProfessionalInfo.OAuthPlatform,
		UserType:      model.UserUserTypeProfessional,
		Status:        xmodel.UserStatusEnable,
	}
	if registerProfessionalInfo.Password != "" {
		user.Password = xtool.EncodeStringWithSalt(registerProfessionalInfo.Password, xconfig.AppConf.PasswordSalt)
	}
	if err = db.Create(&user).Error; err != nil {
		return user, err
	}

	// 綁定角色
	var role xmodel.Role
	if err = db.
		Where("user_type = ?", model.UserUserTypeProfessional).
		Where("role_code = ?", model.UserUserTypeProfessional).
		First(&role).Error; err != nil {
		return user, err
	}
	userRole := xmodel.UserRole{
		UserId: user.Id,
		RoleId: role.Id,
	}
	if err = db.Create(&userRole).Error; err != nil {
		return user, err
	}

	professionalProfile := model.ProfessionalProfile{
		Version:                              "v0.1",
		EmergencyContactFirstName:            "",
		EmergencyContactLastName:             "",
		EmergencyContactPhone:                "",
		EmergencyContactRelationship:         "",
		Experiences:                          []model.ProfessionalExperience{},
		CompletedStudiesInLastThreeYears:     "",
		Qualification:                        "",
		QualificationEndDate:                 "",
		References:                           []model.ProfessionalReference{},
		HasOverseasCitizenshipOrPr:           "",
		RequiresStatutoryDeclaration:         "",
		HasCompletedInfectionControlTraining: "",
		DisclosureQuestions:                  []model.ProfessionalDisclosureQuestion{},
		Files:                                []model.ProfessionalProfileFile{},
	}

	nowTime := time.Now().UTC().Truncate(time.Second)
	// 創建專業人士
	professional := model.Professional{
		UserId:            user.Id,
		DataType:          model.ProfessionalDataTypeDraft,
		FirstName:         "",
		LastName:          "",
		MinimumHourlyRate: decimal.Zero,
		PreferredState:    "",
		PreferredLocality: "",
		DistanceWithin:    decimal.Zero,
		PermissionToWork:  "",
		Profession:        "",
		Gender:            "",
		DateOfBirth:       xtype.NewNullDate(),
		Address:           "",
		LocationLat:       decimal.Zero,
		LocationLng:       decimal.Zero,
		ExperienceLevel:   "",
		AhpraNumber:       "",
		AhpraExpiryDate:   xtype.NewNullDate(),
		AbnNumber:         "",
		IdCheckExpiryDate: xtype.NewNullDate(),
		SignedAgreement:   "",
		ProfileJson:       "",
		ApprovedUserId:    0,
		ApprovedTime:      nil,
		RejectReason:      "",
		Status:            model.ProfessionalStatusPending,
		CreateTime:        nowTime,
		UpdateTime:        nowTime,
	}
	err = professional.MarshalProfile(professionalProfile)
	if err != nil {
		return user, err
	}
	if err = db.Create(&professional).Error; err != nil {
		return user, err
	}
	// 創建專業人士銀行賬戶
	professionalBankAccount := model.ProfessionalBankAccount{
		UserId:            user.Id,
		BankStateBranch:   "",
		BankAccountNumber: "",
		BankAccountName:   "",
	}
	if err = db.Create(&professionalBankAccount).Error; err != nil {
		return user, err
	}

	// TODO: 專業人士註冊完成歡迎通知 - 通知Professional
	// 發送歡迎通知給新註冊的專業人士
	// 通知內容：歡迎加入Medic Crew！請完善您的個人資料以開始尋找工作機會

	return user, nil
}

// endregion ---------------------------------------------------- 專業人士驗證電郵確認註冊 ----------------------------------------------------

// region ---------------------------------------------------- 重發驗證碼電郵 ----------------------------------------------------

type ResendVerificationEmailReq struct {
	UserType        string `json:"userType" binding:"required,oneof=FACILITY PROFESSIONAL"` // 用戶類型 FACILITY=機構，PROFESSIONAL=專業人士
	EmailVerifyUuid string `json:"emailVerifyUuid" binding:"required"`                      // 電郵驗證碼UUID
}

type ResendVerificationEmailResp struct {
	CodeExpiredTime   int64 `json:"codeExpiredTime"`   // 電郵驗證碼過期時間(時間戳)
	RecordExpiredTime int64 `json:"recordExpiredTime"` // 註冊記錄過期時間(時間戳)
}

func (s *registerService) ResendVerificationEmail(c *gin.Context, registerUserType string, req ResendVerificationEmailReq) (ResendVerificationEmailResp, error) {
	var err error
	var resp ResendVerificationEmailResp
	key := s.GetRegisterInfoKey(registerUserType, req.EmailVerifyUuid)

	var professionalInfo RegisterProfessionalInfo
	var facilityInfo RegisterFacilityInfo
	var verifyInfo RegisterVerifyInfo
	var exist bool

	switch registerUserType {
	case RegisterUserTypeProfessional:
		if exist, err = xredis.GetStruct(c, key, &professionalInfo); err != nil {
			return resp, err
		} else if !exist {
			return resp, errors.New("register info not found")
		}
		if err = professionalInfo.GenerateCode(); err != nil {
			return resp, err
		}
		professionalInfo.RegisterVerifyInfo.SendCount++
		err = xredis.UpdateStruct(c, key, &professionalInfo, registerRecordExpiredTime)
		if err != nil {
			return resp, err
		}
		verifyInfo = professionalInfo.RegisterVerifyInfo
	case RegisterUserTypeFacility:
		if exist, err = xredis.GetStruct(c, key, &facilityInfo); err != nil {
			return resp, err
		} else if !exist {
			return resp, errors.New("register info not found")
		}
		if err = facilityInfo.GenerateCode(); err != nil {
			return resp, err
		}
		facilityInfo.RegisterVerifyInfo.SendCount++
		err = xredis.UpdateStruct(c, key, &facilityInfo, registerRecordExpiredTime)
		if err != nil {
			return resp, err
		}
		verifyInfo = facilityInfo.RegisterVerifyInfo
	default:
		return resp, errors.New("invalid register user type")
	}
	// 發送電郵
	nc := xapp.NGinCtx{C: c}
	lang := nc.GetLanguage()
	err = s.sendMailCode(verifyInfo.Code, verifyInfo.Email, lang)
	if err != nil {
		return resp, err
	}

	_ = copier.Copy(&resp, verifyInfo)
	return resp, nil
}

// endregion ---------------------------------------------------- 重發驗證碼電郵 ----------------------------------------------------

// region ---------------------------------------------------- 讀取註冊信息 ----------------------------------------------------

func (s *registerService) GetRegisterInfo(c *gin.Context, registerUserType string, emailVerifyUuid string) (interface{}, error) {
	key := s.GetRegisterInfoKey(registerUserType, emailVerifyUuid)
	var err error
	var exist bool
	var facilityInfo RegisterFacilityInfo
	var professionalInfo RegisterProfessionalInfo
	var registerInfo interface{}

	switch registerUserType {
	case RegisterUserTypeFacility:
		exist, err = xredis.GetStruct(c, key, &facilityInfo)
		registerInfo = facilityInfo
	case RegisterUserTypeProfessional:
		exist, err = xredis.GetStruct(c, key, &professionalInfo)
		registerInfo = professionalInfo
	}
	if err != nil {
		return nil, err
	}
	if !exist {
		return nil, errors.New("register info not found")
	}
	return registerInfo, nil
}

// endregion ---------------------------------------------------- 刪除註冊信息 ----------------------------------------------------
// region ---------------------------------------------------- 刪除註冊信息 ----------------------------------------------------

func (s *registerService) DeleteRegisterInfo(c *gin.Context, registerUserType string, emailVerifyUuid string) error {
	key := s.GetRegisterInfoKey(registerUserType, emailVerifyUuid)
	return xredis.DeleteKey(c, key)
}

// endregion ---------------------------------------------------- 刪除註冊信息 ----------------------------------------------------
