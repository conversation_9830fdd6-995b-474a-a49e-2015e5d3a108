package services

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Norray/xrocket/xgorm"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xmodel/xtype"
	"github.com/Norray/xrocket/xs3"
	"github.com/Norray/xrocket/xtool"
	"github.com/jinzhu/copier"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var InvoiceService = new(invoiceService)

type invoiceService struct{}

const (
	InvoiceDocumentNoPrefix    = "IN%s" // 發票號碼前綴 IN + month + year
	CreditNoteDocumentNoPrefix = "CN%s" // 發票號碼前綴 IN + month + year
)

// region ---------------------------------------------------- Checker ----------------------------------------------------

type CheckInvoiceItemValidReq struct {
	Item        InvoiceItemSummaryReq `json:"item" binding:"required,dive"`
	TotalAmount decimal.Decimal       `json:"totalAmount"`
	TaxAmount   decimal.Decimal       `json:"taxAmount"`
	GrandTotal  decimal.Decimal       `json:"grandTotal"`
}

type InvoiceItemSummary struct {
	TotalAmount decimal.Decimal `json:"totalAmount"`
	TaxAmount   decimal.Decimal `json:"taxAmount"`
	GrandTotal  decimal.Decimal `json:"grandTotal"`
}

func (s *invoiceService) CheckInvoiceItemValid(req CheckInvoiceItemValidReq, taxRate decimal.Decimal) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.invoice.invalid_amount_summary",
		Other: "Invalid amount summary.",
	}

	pass, summary := s.CheckItems(req.Item, taxRate)
	if !pass {
		return false, msg, nil
	}

	if !summary.TotalAmount.Equal(req.TotalAmount) ||
		!summary.TaxAmount.Equal(req.TaxAmount) ||
		!summary.GrandTotal.Equal(req.GrandTotal) {
		return false, msg, nil
	}

	return true, msg, nil
}

func (s *invoiceService) CheckItems(req InvoiceItemSummaryReq, taxRate decimal.Decimal) (bool, InvoiceItemSummary) {
	var itemSummary InvoiceItemSummary

	// 計算 Subtotal
	for _, item := range req.Items {
		amount := item.TotalAmount

		itemSummary.TotalAmount = itemSummary.TotalAmount.Add(amount)

		itemTaxAmount := decimal.Zero
		if item.CountTax == "Y" {
			itemTaxAmount = amount.Mul(taxRate).Round(2)
		}
		itemSummary.TaxAmount = itemSummary.TaxAmount.Add(itemTaxAmount)

		itemSummary.GrandTotal = itemSummary.GrandTotal.Add(amount).Add(itemTaxAmount)
	}

	if !itemSummary.TotalAmount.Equal(req.TotalAmount) ||
		!itemSummary.TaxAmount.Equal(req.TaxAmount) ||
		!itemSummary.GrandTotal.Equal(req.GrandTotal) {
		return false, itemSummary
	}
	return true, itemSummary
}

func (s *invoiceService) CheckCanConfirmPayment(db *gorm.DB, documentIds []uint64, action string) (bool, i18n.Message, error) {
	msg := i18n.Message{
		ID:    "checker.invoice.invalid_payment_status",
		Other: "Invalid payment status.",
	}
	documentIds = xtool.Uint64ArrayDeduplication(documentIds)
	paidStatus := ""
	if action == "CONFIRM" {
		paidStatus = model.DocumentPaidN // 確認付款需狀態為未付款
	} else if action == "CANCEL" {
		paidStatus = model.DocumentPaidY // 撤銷付款需狀態為已付款
	}
	var count int64
	if err := db.Model(&model.Document{}).
		Where("id IN (?)", documentIds).
		Where("paid = ?", paidStatus).
		Count(&count).Error; err != nil {
		return false, i18n.Message{}, err
	}
	if count != int64(len(documentIds)) {
		return false, msg, nil
	}
	return true, msg, nil
}

// 檢查是否可以修改
func (s *invoiceService) CheckCanEdit(db *gorm.DB, document *model.Document) (bool, i18n.Message, error) {
	// 單據已發送後，不能修改
	msg := i18n.Message{
		ID:    "checker.invoice.admin.edit_sent_document",
		Other: "The document has been sent and cannot be edited.",
	}

	if document.SentTime != nil {
		return false, msg, nil
	}
	return true, msg, nil
}

// endregion ---------------------------------------------------- Checker ----------------------------------------------------

// region ---------------------------------------------------- 專業人士列表 ----------------------------------------------------

type ProfessionalInvoiceListReq struct {
	DocumentNo      string `form:"documentNo"`                                        // 確認通知單號碼
	StartDate       string `form:"startDate" binding:"omitempty,datetime=2006-01-02"` // 開始日期
	EndDate         string `form:"endDate" binding:"omitempty,datetime=2006-01-02"`   // 結束日期
	ToName          string `form:"toName"`                                            // 付款人
	FromName        string `form:"fromName"`
	Particular      string `form:"particular"`
	Progress        string `form:"progress"`
	JobId           uint64 `form:"jobId"`                                         // 工作ID
	PaymentReceived string `form:"paymentReceived" binding:"omitempty,oneof=Y N"` // 是否已收到付款 Y/N
}

type ProfessionalInvoiceListSummaryResp struct {
	List       []ProfessionalInvoiceListResp `json:"list" gorm:"-"`
	GrandTotal decimal.Decimal               `json:"grandTotal"`
}

type ProfessionalInvoiceListResp struct {
	FromDocumentId   uint64          `json:"fromDocumentId"`
	DocumentId       uint64          `json:"documentId"`
	DocumentNo       string          `json:"documentNo"`
	DocumentDate     xtype.Date      `swaggertype:"string" json:"documentDate"`
	JobApplicationId uint64          `json:"jobApplicationId"`
	ProfessionalId   uint64          `json:"professionalId"`
	FromName         string          `json:"fromName"` // 發件人姓名
	ToName           string          `json:"toName"`
	Particular       string          `json:"particular"`
	GrandTotal       decimal.Decimal `json:"grandTotal"`
	PaymentReceived  string          `json:"paymentReceived"` // 是否已收到付款 Y/N
}

func (s *invoiceService) ProfessionalList(db *gorm.DB, req ProfessionalInvoiceListReq, userId uint64, pageSet *xresp.PageSet, sortSet xresp.SortingSet) (ProfessionalInvoiceListSummaryResp, error) {
	var err error
	var resp []ProfessionalInvoiceListResp
	var summary ProfessionalInvoiceListSummaryResp
	builder := db.Table("document AS d").
		Joins("JOIN job_application AS ja ON ja.id = d.job_application_id").
		Where("d.user_id = ?", userId).
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.data_type <> ?", model.DocumentDataTypeSystemToFacility). // 系統開立給機構 的發票不在此查詢範圍內
		Where("d.document_no <> ''")                                       // DocumentNo 為空的數據不可比查出
	if req.PaymentReceived != "" {
		builder = builder.Where("d.payment_received = ?", req.PaymentReceived)
	}
	if req.DocumentNo != "" {
		builder = builder.Where("d.document_no LIKE ?", xgorm.EscapeLikeWithWildcards(req.DocumentNo))
	}
	if req.StartDate != "" {
		builder = builder.Where("d.document_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		builder = builder.Where("d.document_date <= ?", req.EndDate)
	}
	if req.ToName != "" {
		builder = builder.Where("d.to_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.ToName))
	}
	if req.FromName != "" {
		builder = builder.Where("d.from_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.FromName))
	}
	if req.Particular != "" {
		builder = builder.Where("d.particular LIKE ?", xgorm.EscapeLikeWithWildcards(req.Particular))
	}
	if req.Progress != "" {
		builder = builder.Where("d.progress = ?", req.Progress)
	}
	if req.JobId > 0 {
		builder = builder.Where("ja.job_id = ?", req.JobId)
	}
	totalAmountBuilder := builder.Session(&gorm.Session{})
	if err = totalAmountBuilder.Select("IFNULL(SUM(d.grand_total),0) AS grand_total").Scan(&summary).Error; err != nil {
		return summary, err
	}
	sortKeyList := map[string]string{
		"documentDate": "d.document_date",
		"grandTotal":   "d.grand_total",
	}
	sortSet.DefaultSortingSql = "d.document_date DESC"
	if err = builder.Select([]string{
		"d.from_document_id",
		"d.id AS document_id",
		"d.document_no",
		"d.document_date",
		"d.job_application_id",
		"d.professional_id",
		"d.from_name",
		"d.to_name",
		"d.particular",
		"d.grand_total",
		"d.payment_received",
	}).Scopes(xresp.Paginate(pageSet)).Scopes(xresp.AddOrder(sortSet, sortKeyList)).Order("d.id DESC").Find(&resp).Error; err != nil {
		return summary, err
	}
	summary.List = resp
	return summary, nil
}

// endregion ---------------------------------------------------- 專業人士列表 ----------------------------------------------------

// region ---------------------------------------------------- 機構列表 ----------------------------------------------------

type FacilityInvoiceListReq struct {
	FromDocumentId uint64 `form:"fromDocumentId"`                                    // 關聯的賬單ID
	DocumentNo     string `form:"documentNo"`                                        // 發票號碼
	StartDate      string `form:"startDate" binding:"omitempty,datetime=2006-01-02"` // 開始日期
	EndDate        string `form:"endDate" binding:"omitempty,datetime=2006-01-02"`   // 結束日期
	ToName         string `form:"toName"`                                            // 付款人
	FromName       string `form:"fromName"`
	Particular     string `form:"particular"`
	Progress       string `form:"progress"`
	JobId          uint64 `form:"jobId"` // 工作ID
}

type FacilityInvoiceListSummaryResp struct {
	GrandTotal decimal.Decimal           `json:"grandTotal"`
	List       []FacilityInvoiceListResp `json:"list" gorm:"-"`
}

type FacilityInvoiceListResp struct {
	FromDocumentId   uint64          `json:"fromDocumentId"`
	DocumentId       uint64          `json:"documentId"`
	DocumentNo       string          `json:"documentNo"`
	DocumentDate     xtype.Date      `swaggertype:"string" json:"documentDate"`
	JobApplicationId uint64          `json:"jobApplicationId"`
	ProfessionalId   uint64          `json:"professionalId"`
	FromName         string          `json:"fromName"` // 發件人姓名
	ToName           string          `json:"toName"`
	Particular       string          `json:"particular"`
	GrandTotal       decimal.Decimal `json:"grandTotal"`
}

func (s *invoiceService) FacilityList(db *gorm.DB, req FacilityInvoiceListReq, facilityId uint64, pageSet *xresp.PageSet, sortSet xresp.SortingSet) (FacilityInvoiceListSummaryResp, error) {
	var err error
	var resp []FacilityInvoiceListResp
	var summary FacilityInvoiceListSummaryResp

	builder := db.Table("document AS d").
		Joins("JOIN job_application AS ja ON ja.id = d.job_application_id").
		Where("d.facility_id = ?", facilityId).
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.data_type <> ?", model.DocumentDataTypeProfessionalToSystem). // 專業人員開立給系統 的發票不在此查詢範圍內
		Where("d.document_no <> ''")                                           // DocumentNo 為空的數據不可比查出

	if req.FromDocumentId > 0 {
		builder = builder.Where("d.from_document_id = ?", req.FromDocumentId)
	}
	if req.DocumentNo != "" {
		builder = builder.Where("d.document_no LIKE ?", xgorm.EscapeLikeWithWildcards(req.DocumentNo))
	}
	if req.Progress != "" {
		builder = builder.Where("d.progress = ?", req.Progress)
	}
	if req.StartDate != "" {
		builder = builder.Where("d.document_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		builder = builder.Where("d.document_date <= ?", req.EndDate)
	}
	if req.ToName != "" {
		builder = builder.Where("d.to_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.ToName))
	}
	if req.FromName != "" {
		builder = builder.Where("d.from_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.FromName))
	}
	if req.Particular != "" {
		builder = builder.Where("d.particular LIKE ?", xgorm.EscapeLikeWithWildcards(req.Particular))
	}
	if req.JobId > 0 {
		builder = builder.Where("ja.job_id = ?", req.JobId)
	}

	totalAmountBuilder := builder.Session(&gorm.Session{})
	if err = totalAmountBuilder.Select("IFNULL(SUM(d.grand_total),0) AS grand_total").Scan(&summary).Error; err != nil {
		return summary, err
	}
	sortKeyList := map[string]string{
		"documentDate": "d.document_date",
		"grandTotal":   "d.grand_total",
	}
	sortSet.DefaultSortingSql = "d.document_date DESC"
	if err = builder.
		Select([]string{
			"d.from_document_id",
			"d.id AS document_id",
			"d.document_no",
			"d.document_date",
			"d.job_application_id",
			"d.professional_id",
			"d.from_name",
			"d.to_name",
			"d.particular",
			"d.grand_total",
		}).Scopes(xresp.Paginate(pageSet)).Scopes(xresp.AddOrder(sortSet, sortKeyList)).Order("d.id DESC").Find(&resp).Error; err != nil {
		return summary, err
	}
	summary.List = resp

	return summary, nil
}

// endregion ---------------------------------------------------- 機構列表 ----------------------------------------------------

// region ---------------------------------------------------- 查詢發票 ----------------------------------------------------

type InvoiceInquireReq struct {
	DocumentId uint64 `form:"documentId" binding:"required"` // 發票ID
}

type InvoiceInquireResp struct {
	FromDocumentId        uint64                   `json:"fromDocumentId"`
	DocumentId            uint64                   `json:"documentId"`
	DocumentNo            string                   `json:"documentNo"`
	DocumentDate          xtype.Date               `swaggertype:"string" json:"documentDate"`
	DueDate               xtype.NullDate           `swaggertype:"string" json:"dueDate"`
	ToSystem              string                   `json:"toSystem"`
	JobId                 uint64                   `json:"jobId"`
	JobApplicationId      uint64                   `json:"jobApplicationId"`
	ProfessionalId        uint64                   `json:"professionalId"`
	UserId                uint64                   `json:"userId"`
	FacilityId            uint64                   `json:"facilityId"`
	FromName              string                   `json:"fromName"`
	FromAddress           string                   `json:"fromAddress"`
	FromEmail             string                   `json:"fromEmail"`
	FromBankStateBranch   string                   `json:"fromBankStateBranch"`
	FromBankAccountNumber string                   `json:"fromBankAccountNumber"`
	FromAbn               string                   `json:"fromAbn"`
	ToName                string                   `json:"toName"`
	ToAddress             string                   `json:"toAddress"`
	ToEmail               string                   `json:"toEmail"`
	ToAbn                 string                   `json:"toAbn"`
	Status                string                   `json:"status"`
	TotalAmount           decimal.Decimal          `json:"totalAmount"`
	TaxAmount             decimal.Decimal          `json:"taxAmount"`
	TaxRate               decimal.Decimal          `json:"taxRate"`
	GrandTotal            decimal.Decimal          `json:"grandTotal"`
	Remark                string                   `json:"remark"`
	OtherRemark           string                   `json:"otherRemark"`
	PaymentReceived       string                   `json:"paymentReceived"`
	Items                 []InvoiceItemInquireResp `json:"items"`
	WagesFiles            []InvoiceFileInquireResp `json:"wagesFiles"`
	OtherFiles            []InvoiceFileInquireResp `json:"otherFiles"`
}

type InvoiceItemInquireResp struct {
	DocumentId  uint64          `json:"documentId"`
	ItemType    string          `json:"itemType"`
	ItemName    string          `json:"itemName"`
	Seq         int32           `json:"seq"`
	JobShiftId  uint64          `json:"jobShiftId"`
	ItemDate    string          `json:"date"`
	StartTime   string          `json:"startTime"`
	NextDay     string          `json:"nextDay"`
	FinishTime  string          `json:"endTime"`
	Particular  string          `json:"particular"`
	Hours       decimal.Decimal `json:"hours"`
	HourlyRate  decimal.Decimal `json:"rate"`
	TaxAmount   decimal.Decimal `json:"taxAmount"`
	TotalAmount decimal.Decimal `json:"totalAmount"`
	CountTax    string          `json:"countTax"`
}

type InvoiceFileInquireResp struct {
	DocumentFileId uint64 `json:"documentFileId"`
	Uuid           string `json:"uuid"`
	OriginFileName string `json:"originFileName"`
	Link           string `json:"link"`
	FileType       string `json:"fileType"`
	FileSize       uint32 `json:"fileSize"`
}

func (s *invoiceService) Inquire(db *gorm.DB, req InvoiceInquireReq) (InvoiceInquireResp, error) {
	var err error
	var resp InvoiceInquireResp
	var document model.Document

	// 查詢發票
	if err = db.Where("category = ?", model.DocumentCategoryInvoice).First(&document, req.DocumentId).Error; err != nil {
		return resp, err
	}

	// 查詢工作申請
	var jobApplication model.JobApplication
	if document.JobApplicationId > 0 {
		if err = db.First(&jobApplication, document.JobApplicationId).Error; err != nil {
			return resp, err
		}
		resp.JobId = jobApplication.JobId
	}

	// 組裝響應
	_ = copier.Copy(&resp, &document)

	resp.ToSystem = "N"
	if document.DataType == model.DocumentDataTypeProfessionalToSystem {
		resp.ToSystem = "Y"
	}

	// 查詢項目
	var documentItems []model.DocumentItem
	if err = db.Where("document_id = ?", req.DocumentId).Order("seq ASC").Find(&documentItems).Error; err != nil {
		return resp, err
	}

	resp.Items = make([]InvoiceItemInquireResp, 0)
	for _, documentItem := range documentItems {
		resultItem := InvoiceItemInquireResp{
			DocumentId:  documentItem.DocumentId,
			ItemType:    documentItem.ItemType,
			ItemName:    documentItem.ItemName,
			Seq:         documentItem.Seq,
			JobShiftId:  documentItem.JobShiftId,
			ItemDate:    documentItem.ItemDate.Date.String(),
			StartTime:   documentItem.StartTime,
			NextDay:     documentItem.NextDay,
			FinishTime:  documentItem.FinishTime,
			Particular:  documentItem.Particular,
			Hours:       documentItem.Hours,
			HourlyRate:  documentItem.HourlyRate,
			TaxAmount:   documentItem.TaxAmount,
			TotalAmount: documentItem.TotalAmount,
			CountTax:    documentItem.CountTax,
		}
		resp.Items = append(resp.Items, resultItem)
		resp.TotalAmount = resp.TotalAmount.Add(documentItem.TotalAmount)
		resp.TaxAmount = resp.TaxAmount.Add(documentItem.TaxAmount)
		resp.GrandTotal = resp.GrandTotal.Add(documentItem.TotalAmount).Add(documentItem.TaxAmount)
	}

	// 查詢文件
	var documentFiles []model.DocumentFile
	if err = db.
		Table("document_file AS df").
		Joins("JOIN document_file_relation AS dfr ON dfr.document_file_id = df.id").
		Select([]string{
			"df.*",
		}).
		Where("dfr.document_id = ?", req.DocumentId).
		Order("dfr.seq ASC").
		Find(&documentFiles).Error; err != nil {
		return resp, err
	}
	resp.WagesFiles = make([]InvoiceFileInquireResp, 0)
	resp.OtherFiles = make([]InvoiceFileInquireResp, 0)
	for _, documentFile := range documentFiles {
		resultFile := InvoiceFileInquireResp{
			DocumentFileId: documentFile.Id,
			Uuid:           documentFile.Uuid,
			OriginFileName: documentFile.OriginFileName,
			FileType:       documentFile.FileType,
			FileSize:       documentFile.FileSize,
		}

		if resultFile.Link, err = xs3.GetPresignDownloadUrl(xconfig.OSSConf.Bucket, documentFile.Path, xs3.StandardExpiredDuration); err != nil {
			return resp, err
		}

		if documentFile.FileCode == model.DocumentFileCodeWages {
			resp.WagesFiles = append(resp.WagesFiles, resultFile)
		} else {
			resp.OtherFiles = append(resp.OtherFiles, resultFile)
		}
	}

	return resp, nil
}

// endregion ---------------------------------------------------- 查詢發票 ----------------------------------------------------

// region ---------------------------------------------------- 專業人士 - 發票標記已收款 ----------------------------------------------------

type InvoicePaymentReceivedReq struct {
	DocumentId      uint64 `json:"documentId" binding:"required"`
	PaymentReceived string `json:"paymentReceived" binding:"required,oneof=Y N"`
	ReqUserId       uint64 `json:"-"`
}

func (s *invoiceService) InvoicePaymentReceived(db *gorm.DB, req InvoicePaymentReceivedReq) error {
	var doc model.Document
	if err := db.Where("id = ?", req.DocumentId).Where("user_id = ?", req.ReqUserId).First(&doc).Error; err != nil {
		return err
	}
	if err := db.Model(&doc).Update("payment_received", req.PaymentReceived).Error; err != nil {
		return err
	}
	return nil
}

// endregion ---------------------------------------------------- 專業人士 - 發票標記已收款 ----------------------------------------------------

// region ---------------------------------------------------- 管理端列表 ----------------------------------------------------

type SystemInvoiceListReq struct {
	FromDocumentId   uint64 `form:"fromDocumentId"`                                    // 關聯的賬單ID
	DocumentNo       string `form:"documentNo"`                                        // 發票號碼
	StartDate        string `form:"startDate" binding:"omitempty,datetime=2006-01-02"` // 開始日期
	EndDate          string `form:"endDate" binding:"omitempty,datetime=2006-01-02"`   // 結束日期
	JobApplicationId uint64 `form:"jobApplicationId"`                                  // 工作申請ID
	ToName           string `form:"toName"`
	FromName         string `form:"fromName"`
	Progress         string `form:"progress"`                                   // 進度
	Particular       string `form:"particular"`                                 // 明細
	Paid             string `form:"paid" binding:"omitempty,oneof=Y N"`         // 是否已付款 Y/N
	DataType         string `form:"dataType" binding:"omitempty,oneof=S2F P2S"` // 資料來源 AR=S2F(系統發給機構) AP=P2S(專業人員開立給系統)
}

type SystemInvoiceListSummaryResp struct {
	GrandTotal decimal.Decimal         `json:"grandTotal"`
	List       []SystemInvoiceListResp `json:"list" gorm:"-"`
}

type SystemInvoiceListResp struct {
	DocumentId       uint64          `json:"documentId"`
	FromDocumentId   uint64          `json:"fromDocumentId"`
	DocumentNo       string          `json:"documentNo"`
	DocumentDate     xtype.Date      `swaggertype:"string" json:"documentDate"`
	JobApplicationId uint64          `json:"jobApplicationId"`
	ProfessionalId   uint64          `json:"professionalId"`
	FromName         string          `json:"fromName"` // 發件人姓名
	ToName           string          `json:"toName"`
	GrandTotal       decimal.Decimal `json:"grandTotal"`
	Particular       string          `json:"particular"`
	Paid             string          `json:"paid"`
	PaidDate         xtype.Date      `swaggertype:"string" json:"paidDate"`
	Sent             string          `json:"sent"` // 是否發送 Y/N

}

func (s *invoiceService) SystemList(db *gorm.DB, req SystemInvoiceListReq, pageSet *xresp.PageSet, sortSet xresp.SortingSet) (SystemInvoiceListSummaryResp, error) {
	var err error
	var resp []SystemInvoiceListResp
	var summary SystemInvoiceListSummaryResp

	builder := db.Table("document AS d").
		Where("d.category = ?", model.DocumentCategoryInvoice).
		Where("d.progress = ?", model.DocumentProgressConfirm).
		Where("d.data_type <> ?", model.DocumentDataTypeProfessionalToFacility) // 專業人員開立給機構 的發票不在此查詢範圍內

	if req.FromDocumentId > 0 {
		builder = builder.Where("d.from_document_id = ?", req.FromDocumentId)
	}
	if req.JobApplicationId != 0 {
		builder = builder.Where("d.job_application_id = ?", req.JobApplicationId)
	}
	if req.DocumentNo != "" {
		builder = builder.Where("d.document_no LIKE ?", xgorm.EscapeLikeWithWildcards(req.DocumentNo))
	}
	if req.Progress != "" {
		builder = builder.Where("d.progress = ?", req.Progress)
	}
	if req.StartDate != "" {
		builder = builder.Where("d.document_date >= ?", req.StartDate)
	}
	if req.EndDate != "" {
		builder = builder.Where("d.document_date <= ?", req.EndDate)
	}
	if req.ToName != "" {
		builder = builder.Where("d.to_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.ToName))
	}
	if req.FromName != "" {
		builder = builder.Where("d.from_name LIKE ?", xgorm.EscapeLikeWithWildcards(req.FromName))
	}
	if req.Particular != "" {
		builder = builder.Where("d.particular LIKE ?", xgorm.EscapeLikeWithWildcards(req.Particular))
	}
	if req.DataType != "" {
		builder = builder.Where("d.data_type = ?", req.DataType)
	}
	if req.Paid != "" {
		builder = builder.Where("d.paid = ?", req.Paid)
	}

	totalAmountBuilder := builder.Session(&gorm.Session{})
	if err = totalAmountBuilder.Select("IFNULL(SUM(d.grand_total),0) AS grand_total").Scan(&summary).Error; err != nil {
		return summary, err
	}
	sortKeyList := map[string]string{
		"documentDate": "d.document_date",
		"grandTotal":   "d.grand_total",
	}
	sortSet.DefaultSortingSql = "d.document_date DESC"
	if err = builder.
		Select([]string{
			"d.id AS document_id",
			"d.from_document_id",
			"d.document_no",
			"d.document_date",
			"d.job_application_id",
			"d.professional_id",
			"d.from_name",
			"d.to_name",
			"d.grand_total",
			"d.particular",
			"d.paid",
			"d.paid_date",
			"CASE WHEN d.sent_time IS NOT NULL THEN 'Y' ELSE 'N' END AS sent",
		}).Scopes(xresp.Paginate(pageSet)).Scopes(xresp.AddOrder(sortSet, sortKeyList)).Order("d.id DESC").Find(&resp).Error; err != nil {
		return summary, err
	}
	summary.List = resp

	return summary, nil
}

// endregion ---------------------------------------------------- 管理端列表 ----------------------------------------------------

// region ---------------------------------------------------- 管理端 - 新增發票 ----------------------------------------------------

type InvoiceCreateReq struct {
	FacilityId  uint64                `json:"facilityId" binding:"required"` // 機構ID
	ToName      string                `json:"toName" binding:"required"`     // To 姓名
	ToAddress   string                `json:"toAddress" binding:"required"`  // To 地址
	ToAbn       string                `json:"toAbn" binding:"required"`      // To ABN
	Item        InvoiceItemSummaryReq `json:"item" binding:"required"`       // 項目
	Remark      string                `json:"remark"`                        // 備註
	TotalAmount decimal.Decimal       `json:"totalAmount"`                   // 合計
	TaxAmount   decimal.Decimal       `json:"taxAmount"`                     // 稅金
	GrandTotal  decimal.Decimal       `json:"grandTotal"`                    // 總計
	ReqUserId   uint64                `json:"-"`
}

type InvoiceItemSummaryReq struct {
	Items       []InvoiceItemReq `json:"items" binding:"required,dive"`
	TotalAmount decimal.Decimal  `json:"totalAmount"` // 合計
	TaxAmount   decimal.Decimal  `json:"taxAmount"`   // 稅金
	GrandTotal  decimal.Decimal  `json:"grandTotal"`  // 總計
}

type InvoiceItemReq struct {
	ItemDate    string          `json:"itemDate" binding:"omitempty,datetime=2006-01-02"` // 日期(YYYY-MM-DD)
	ItemName    string          `json:"itemName" binding:"omitempty"`                     // 項目名稱
	Particular  string          `json:"particular" binding:"required"`                    // 備註
	TaxAmount   decimal.Decimal `json:"taxAmount"`                                        // 稅金
	TotalAmount decimal.Decimal `json:"totalAmount" binding:"required"`                   // 總金額
	CountTax    string          `json:"countTax" binding:"required,oneof=Y N"`            // 是否計算稅額 Y N
}

type InvoiceCreateResp struct {
	DocumentId uint64 `json:"documentId"`
}

func (s *invoiceService) Create(db *gorm.DB, req InvoiceCreateReq, taxRate decimal.Decimal) (InvoiceCreateResp, error) {
	nowTime := time.Now().UTC().Truncate(time.Second)
	var resp InvoiceCreateResp
	var err error

	// 查詢機構資料
	var facilityProfile model.FacilityProfile
	if err = db.Where("facility_id = ?", req.FacilityId).
		Where("data_type = ?", model.FacilityProfileDataTypeApproved).
		First(&facilityProfile).Error; err != nil {
		return resp, err
	}

	dataType := model.DocumentDataTypeSystemToFacility
	documentNo, seqNo, err := s.GenerateDocumentNo(db, model.DocumentCategoryInvoice, dataType, nowTime)
	if err != nil {
		return resp, err
	}
	documentItems, particulars := s.generateItems(req.Item)

	var platformProfile PlatformProfile
	platformProfile, err = CommonSettingService.GetPlatformProfile(db)
	if err != nil {
		return resp, err
	}

	today := time.Now().Format(xtool.DateDayA) // 服務器的時區(用戶的時區)
	dueDate := time.Now().AddDate(0, 0, 7).Format(xtool.DateDayA)

	document := model.Document{
		Category:              model.DocumentCategoryInvoice,
		FacilityId:            req.FacilityId,
		DataType:              dataType,
		SeqNo:                 seqNo,
		DocumentNo:            xtype.NotNullString(documentNo),
		DocumentDate:          xtype.NewDate(today),
		DueDate:               xtype.NewNullDate(dueDate),
		FromName:              platformProfile.PlatformName,
		FromEmail:             platformProfile.PlatformEmail,
		FromAddress:           platformProfile.PlatformAddress,
		FromBankStateBranch:   platformProfile.PlatformBankStateBranch,
		FromBankAccountNumber: platformProfile.PlatformBankAccountNumber,
		FromBankAccountName:   platformProfile.PlatformBankAccountName,
		ToName:                req.ToName,
		ToEmail:               facilityProfile.Email,
		ToAddress:             req.ToAddress,
		ToAbn:                 req.ToAbn,
		Particular:            particulars,
		TotalAmount:           req.TotalAmount,
		TaxAmount:             req.TaxAmount,
		TaxRate:               taxRate,
		SuperRate:             decimal.Zero,
		GrandTotal:            req.GrandTotal,
		Progress:              model.DocumentProgressConfirm,
		CreateTime:            nowTime,
		CreateUserId:          req.ReqUserId,
		Remark:                req.Remark,
		Paid:                  model.DocumentPaidN,
		OtherRemark:           "",
	}

	// 保存確認通知單
	if err = db.Create(&document).Error; err != nil {
		return resp, err
	}

	// Facility收到新的發票 - 通知Facility
	// 發送發票接收通知給機構
	dueDateStr := ""
	if document.DueDate.Valid {
		dueDateStr = document.DueDate.String()
	}
	if err = SystemNotificationService.CreateFacilityBillingInvoiceReceived(db, CreateFacilityBillingInvoiceReceivedReq{
		JobApplicationId: document.JobApplicationId,
		InvoiceId:        document.Id,
		GrandTotal:       document.GrandTotal,
		DueDate:          dueDateStr,
	}); err != nil {
		return resp, err
	}

	// 存儲
	for _, documentItem := range documentItems {
		documentItem.DocumentId = document.Id
	}

	if err = db.Create(&documentItems).Error; err != nil {
		return resp, err
	}

	resp.DocumentId = document.Id
	return resp, nil
}

func (s *invoiceService) generateItems(req InvoiceItemSummaryReq) ([]*model.DocumentItem, string) {
	var particulars []string
	documentItems := make([]*model.DocumentItem, 0)

	seq := int32(1)
	for _, item := range req.Items {
		var documentItem model.DocumentItem
		_ = copier.Copy(&documentItem, item)
		documentItem.ItemType = model.DocumentItemTypeCustom
		documentItem.ItemDate = xtype.NewNullDate(item.ItemDate) // 日期轉換
		documentItem.Seq = seq
		documentItems = append(documentItems, &documentItem)
		particulars = append(particulars, s.generateParticulars(documentItem))
		seq += 1
	}

	return documentItems, strings.Join(particulars, "; ")
}

func (s *invoiceService) generateParticulars(documentItem model.DocumentItem) string {
	particular := fmt.Sprintf("%s, $%s", documentItem.Particular, ConfirmationNoteService.CustomerHumanizeAmount(documentItem.TotalAmount, 2))
	if documentItem.ItemType == model.DocumentItemTypeCustom && documentItem.ItemName != "" {
		particular = fmt.Sprintf("%s (%s)", documentItem.ItemName, particular)
	}
	return particular
}

// endregion ---------------------------------------------------- 管理端 - 新增發票 ----------------------------------------------------

// region ---------------------------------------------------- 發票號碼生成器 ----------------------------------------------------
func (s *invoiceService) GetDocumentLastSeqNo(db *gorm.DB, category string, dataType string, date time.Time) (int32, error) {
	var seqNo int32
	var preDocumentNo string
	switch category {
	case model.DocumentCategoryInvoice:
		preDocumentNo = fmt.Sprintf(InvoiceDocumentNoPrefix, date.Format("012006"))
	case model.DocumentCategoryCreditNote:
		preDocumentNo = fmt.Sprintf(CreditNoteDocumentNoPrefix, date.Format("012006"))
	default:
		return 0, errors.New("invalid category")
	}

	builder := db.Table("document AS d").
		Select([]string{
			"IFNULL(max(seq_no), 0) AS seq_no",
		}).
		Where("d.category = ?", category).
		Where("d.data_type = ?", dataType).
		Where("d.document_no LIKE ?", preDocumentNo+"%")
	if err := builder.Scan(&seqNo).Error; err != nil {
		return 0, err
	}
	return seqNo, nil
}

func (s *invoiceService) GenerateDocumentNo(db *gorm.DB, category string, dataType string, date time.Time) (string, int32, error) {
	lastSeqNo, err := s.GetDocumentLastSeqNo(db, category, dataType, date)
	if err != nil {
		return "", 0, err
	}
	preDocumentNo := fmt.Sprintf(InvoiceDocumentNoPrefix, date.Format("012006"))

	lastSeqNo += 1
	var documentNo string
	// 循環檢查
	for {
		documentNo = fmt.Sprintf("%s%06d", preDocumentNo, lastSeqNo)
		exist, err := ConfirmationNoteService.CheckDocumentNo(db, category, dataType, documentNo)
		if err != nil {
			return "", 0, err
		}
		if !exist {
			break
		}
		lastSeqNo += 1
	}

	return documentNo, lastSeqNo, nil
}

// endregion ---------------------------------------------------- 發票號碼生成器 ----------------------------------------------------

// region ---------------------------------------------------- 修改發票 ----------------------------------------------------

type InvoiceEditReq struct {
	DocumentId  uint64                `json:"documentId" binding:"required"`
	FacilityId  uint64                `json:"facilityId" binding:"required"` // 機構ID
	ToName      string                `json:"toName" binding:"required"`     // To 姓名
	ToAddress   string                `json:"toAddress" binding:"required"`  // To 地址
	ToAbn       string                `json:"toAbn" binding:"required"`      // To ABN
	Item        InvoiceItemSummaryReq `json:"item" binding:"required"`
	Remark      string                `json:"remark"`
	TotalAmount decimal.Decimal       `json:"totalAmount"`
	TaxAmount   decimal.Decimal       `json:"taxAmount"`
	GrandTotal  decimal.Decimal       `json:"grandTotal"`
	ReqUserId   uint64                `json:"-"`
}

func (s *invoiceService) Edit(db *gorm.DB, document model.Document, req InvoiceEditReq, taxRate decimal.Decimal) error {
	var err error

	// 計算總金額和稅金
	documentItems, particulars := s.generateItems(req.Item)

	// 修改發票
	currentTime := time.Now().UTC()
	document.Particular = particulars
	document.TotalAmount = req.TotalAmount
	document.TaxAmount = req.TaxAmount
	document.TaxRate = taxRate
	document.SuperRate = decimal.Zero
	document.CommissionAmount = decimal.Zero
	document.GrandTotal = req.GrandTotal
	document.UpdateTime = &currentTime
	document.UpdateUserId = req.ReqUserId
	document.Remark = req.Remark

	if document.FacilityId > 0 {
		// 查詢機構資料
		var facilityProfile model.FacilityProfile
		if err = db.Where("facility_id = ?", document.FacilityId).
			Where("data_type = ?", model.FacilityProfileDataTypeApproved).
			First(&facilityProfile).Error; err != nil {
			return err
		}
		document.ToEmail = facilityProfile.Email

		document.FacilityId = req.FacilityId
		document.ToName = req.ToName
		document.ToAddress = req.ToAddress
		document.ToAbn = req.ToAbn
	}

	if err = db.Save(&document).Error; err != nil {
		return err
	}

	if err = db.Delete(&model.DocumentItem{}, "document_id = ?", req.DocumentId).Error; err != nil {
		return err
	}

	// 存儲
	for _, documentItem := range documentItems {
		documentItem.DocumentId = document.Id
	}

	if err = db.Create(&documentItems).Error; err != nil {
		return err
	}

	// 更新項目
	if err = db.Delete(&model.DocumentFileRelation{}, "document_id = ?", req.DocumentId).Error; err != nil {
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- 修改發票 ----------------------------------------------------

// region ---------------------------------------------------- 管理端 - 確認付款/撤銷付款 ----------------------------------------------------

type InvoiceConfirmPaymentReq struct {
	DocumentIds []uint64 `json:"documentIds" binding:"required,min=1"`                                           // 發票ID數組
	Action      string   `json:"action" binding:"required,oneof=CONFIRM CANCEL"`                                 // 操作 CONFIRM: 確認付款 CANCEL: 撤銷付款
	PaymentDate string   `json:"paymentDate" binding:"required_if=Action CONFIRM,omitempty,datetime=2006-01-02"` // 付款日期(YYYY-MM-DD)
	ReqUserId   uint64   `json:"-"`
}

func (s *invoiceService) ConfirmPayment(db *gorm.DB, req InvoiceConfirmPaymentReq) error {
	documentIds := xtool.Uint64ArrayDeduplication(req.DocumentIds)
	if err := DocumentService.LockDocuments(db, documentIds); err != nil {
		return err
	}

	var updateMap map[string]interface{}
	switch req.Action {
	case "CONFIRM":
		updateMap = map[string]interface{}{
			"paid":         model.DocumentPaidY,
			"paid_date":    xtype.NewDate(req.PaymentDate),
			"paid_user_id": req.ReqUserId,
		}
	case "CANCEL":
		updateMap = map[string]interface{}{
			"paid":         model.DocumentPaidN,
			"paid_date":    xtype.NewNullDate(),
			"paid_user_id": 0,
		}
	}
	result := db.
		Model(&model.Document{}).
		Where("id IN (?)", documentIds).
		Updates(updateMap)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected != int64(len(documentIds)) {
		return errors.New("some documents are not found")
	}
	return nil
}

// endregion ---------------------------------------------------- 管理端 - 確認付款 ----------------------------------------------------

// region ---------------------------------------------------- 代付機構 - 機構生成發票給機構 ----------------------------------------------------

func (s *invoiceService) CreateUpfrontInvoiceByFacility(db *gorm.DB, jobId uint64) error {
	superRate := decimal.NewFromInt(12).Div(decimal.NewFromInt(100))
	taxRate := decimal.NewFromInt(10).Div(decimal.NewFromInt(100))

	// 查詢工作
	var job model.Job
	if err := db.Where("id = ?", jobId).First(&job).Error; err != nil {
		return err
	}
	if job.PaymentTerms != model.JobPaymentTermsPayUpfront {
		return errors.New("job payment terms is not upfront, jobId: " + strconv.FormatUint(jobId, 10))
	}

	// 機構資料
	var facilityProfile model.FacilityProfile
	if err := db.Where("facility_id = ?", job.FacilityId).
		Where("id = ?", job.FacilityProfileId).
		First(&facilityProfile).Error; err != nil {
		return err
	}
	// 工作班次
	jobShifts := make([]model.JobShift, 0)
	if err := db.Where("job_id = ?", jobId).Find(&jobShifts).Error; err != nil {
		return err
	}
	// 服務地點
	var serviceLocation model.ServiceLocation
	if err := db.Where("id = ?", job.ServiceLocationId).First(&serviceLocation).Error; err != nil {
		return err
	}

	// 查詢錄取的專業人士
	var jobApplications []model.JobApplication
	if err := db.
		Where("job_id = ?", jobId).
		Where("status = ?", model.JobApplicationStatusAccept).
		Where("accept = ?", model.JobApplicationAcceptY).
		Find(&jobApplications).Error; err != nil {
		return err
	}
	// 獲取平台資料
	platformProfile, err := CommonSettingService.GetPlatformProfile(db)
	if err != nil {
		return err
	}

	// 查詢佣金比
	commissionRates, err := s.getCommissionRate(db, jobId)
	if err != nil {
		return err
	}
	// 錄取的專業人士
	for _, jobApplication := range jobApplications {
		// 如果已生成發票，則跳過
		if jobApplication.InvoiceGenerated == model.JobApplicationInvoiceGeneratedY {
			continue
		}
		// 如果申請狀態不是已錄取，則跳過
		if jobApplication.Status != model.JobApplicationStatusAccept || jobApplication.Accept != model.JobApplicationAcceptY {
			continue
		}
		// 生成發票批次，每次最多10個自然日
		var invoiceBatches []InvoiceBatch
		invoiceBatches, err = s.generateInvoice(db, generateInvoiceReq{
			Job:             job,
			JobApplication:  jobApplication,
			JobShifts:       jobShifts,
			ServiceLocation: serviceLocation,
			TaxRate:         taxRate,
			SuperRate:       superRate,
			CommissionRates: commissionRates,
			PlatformProfile: platformProfile,
			FacilityProfile: facilityProfile,
		})
		if err != nil {
			return err
		}
		for _, invoiceBatch := range invoiceBatches {
			if err = db.Create(&invoiceBatch.Invoice).Error; err != nil {
				return err
			}
			for i := range invoiceBatch.Items {
				invoiceBatch.Items[i].DocumentId = invoiceBatch.Invoice.Id
			}
			if err = db.Create(&invoiceBatch.Items).Error; err != nil {
				return err
			}
		}

		// 更新工作申請記錄，標記為已生成發票
		if err = db.Model(&model.JobApplication{}).
			Where("id = ?", jobApplication.Id).
			Updates(map[string]interface{}{
				"invoice_generated": model.JobApplicationInvoiceGeneratedY,
				"invoice_time":      time.Now().UTC().Truncate(time.Second),
			}).Error; err != nil {
			return err
		}
	}
	return nil
}

type InvoiceBatch struct {
	Invoice model.Document
	Items   []model.DocumentItem
}

type generateInvoiceReq struct {
	Job             model.Job
	JobApplication  model.JobApplication
	JobShifts       []model.JobShift
	ServiceLocation model.ServiceLocation
	TaxRate         decimal.Decimal
	SuperRate       decimal.Decimal
	CommissionRates []JobCommissionRate
	PlatformProfile PlatformProfile
	FacilityProfile model.FacilityProfile
}

// 將JobShift拆分成多個發票
func (s *invoiceService) generateInvoice(db *gorm.DB, req generateInvoiceReq) ([]InvoiceBatch, error) {
	var err error
	invoiceBatches := make([]InvoiceBatch, 0)
	nowTime := time.Now().UTC().Truncate(time.Second)
	tz, err := time.LoadLocation(req.ServiceLocation.Timezone)
	if err != nil {
		return nil, err
	}
	hasSuper := false
	var professional model.Professional
	if err = db.Where("id = ?", req.JobApplication.ProfessionalId).First(&professional).Error; err != nil {
		return nil, err
	}
	if !ProfessionalProfileService.CheckIsCompanyAbn(professional) {
		hasSuper = true
	}

	var positionProfession model.Selection
	if err = db.Where("selection_type = ?", model.SelectionTypeProfessionalProfession).
		Where("code = ?", req.Job.PositionProfession).
		First(&positionProfession).Error; err != nil {
		return nil, err
	}

	defaultInvoice := model.Document{
		Id:                    0,
		FromDocumentId:        0,
		Category:              model.DocumentCategoryInvoice,
		JobApplicationId:      req.JobApplication.Id,
		DataType:              model.DocumentDataTypeSystemToFacility,
		SeqNo:                 1,
		DueDate:               xtype.NewNullDate(),
		DocumentNo:            xtype.NullString(),
		FacilityId:            req.Job.FacilityId,
		FromName:              req.PlatformProfile.PlatformName,
		FromEmail:             req.PlatformProfile.PlatformEmail,
		FromAddress:           req.PlatformProfile.PlatformAddress,
		FromBankStateBranch:   req.PlatformProfile.PlatformBankStateBranch,
		FromBankAccountNumber: req.PlatformProfile.PlatformBankAccountNumber,
		FromBankAccountName:   req.PlatformProfile.PlatformBankAccountName,
		ToName:                req.FacilityProfile.Name,
		ToAbn:                 req.FacilityProfile.Abn,
		ToEmail:               req.FacilityProfile.Email,
		ToAddress:             req.FacilityProfile.Address,
		Particular:            "",
		Currency:              "",
		PaymentReceived:       model.DocumentProgressPaymentReceivedN,
		Paid:                  model.DocumentPaidN,
		PaidDate:              xtype.NewNullDate(),
		Progress:              model.DocumentProgressDraft,
		CreateTime:            nowTime,
	}
	invoice := defaultInvoice
	today := time.Now() // 服務器時間
	invoice.DocumentDate = xtype.NewDate(today.Format(xtool.DateDayA))
	dueDate := today.AddDate(0, 0, 7)
	invoice.DueDate = xtype.NewNullDate(dueDate.Format(xtool.DateDayA))
	items := make([]model.DocumentItem, 0)
	dayCount := 0
	lastDays := ""
	itemSeq := int32(1)
	nextDocumentDate := ""
	for jobShiftIndex, jobShift := range req.JobShifts {
		beginTime := jobShift.BeginTime.In(tz)
		beginTimeDayStr := beginTime.Format(xtool.DateDayA)
		endTime := jobShift.EndTime.In(tz)
		endTimeDayStr := endTime.Format(xtool.DateDayA)
		if beginTimeDayStr != lastDays {
			if beginTimeDayStr == lastDays {
				dayCount += 1
			} else {
				dayCount += 2
			}
		} else {
			if endTimeDayStr != lastDays {
				dayCount += 1
			}
		}
		lastDays = endTimeDayStr

		nextDay := "N"
		if beginTimeDayStr != lastDays {
			nextDay = "Y"
		}
		var particular string
		particular, err = s.generateInvoiceWagesItemParticulars(positionProfession.Name, jobShift, tz)
		if err != nil {
			return nil, err
		}

		taxAmount := decimal.Zero
		if jobShift.PayHours.GreaterThan(decimal.Zero) {
			taxAmount = jobShift.PayHours.Mul(jobShift.HourlyRate).Mul(req.TaxRate)
		}

		item := model.DocumentItem{
			Id:            0,
			DocumentId:    0,
			ItemType:      model.DocumentItemTypeWages,
			ItemName:      "",
			Seq:           itemSeq,
			JobShiftId:    jobShift.Id,
			ItemDate:      xtype.NewNullDate(beginTimeDayStr),
			StartTime:     beginTime.Format(xtool.TimeHourG1),
			NextDay:       nextDay,
			FinishTime:    endTime.Format(xtool.TimeHourG1),
			Particular:    particular,
			ExpectedHours: jobShift.Duration,
			Hours:         jobShift.PayHours,
			BreakDuration: jobShift.BreakDuration,
			HourlyRate:    jobShift.HourlyRate,
			TotalAmount:   jobShift.PayHours.Mul(jobShift.HourlyRate).Round(2),
			TaxAmount:     taxAmount,
			CountTax:      "Y",
		}

		items = append(items, item)
		itemSeq += 1

		// 下一批發票的日期
		if nextDocumentDate == "" && dayCount >= 5 {
			if dayCount == 5 {
				nextDocumentDate = beginTime.AddDate(0, 0, 1).Format(xtool.DateDayA)
			} else {
				nextDocumentDate = endTime.AddDate(0, 0, 1).Format(xtool.DateDayA)
			}
		}

		if dayCount >= 10 || jobShiftIndex == len(req.JobShifts)-1 {
			// 生成發票批次
			var invoiceBatch InvoiceBatch
			invoiceBatch, err = s.processInvoiceBatch(invoice, items, req, beginTimeDayStr, hasSuper)
			if err != nil {
				return nil, err
			}
			invoiceBatches = append(invoiceBatches, invoiceBatch)

			// 重置
			dayCount = 0
			lastDays = ""
			itemSeq = int32(1)
			items = make([]model.DocumentItem, 0)
			invoiceSeq := invoice.SeqNo + 1
			invoice = defaultInvoice
			invoice.SeqNo = invoiceSeq
			invoice.DocumentDate = xtype.NewDate(nextDocumentDate)
		}
	}
	return invoiceBatches, nil
}
func (s *invoiceService) processInvoiceBatch(invoice model.Document, items []model.DocumentItem, req generateInvoiceReq, beginTimeDayStr string, hasSuper bool) (InvoiceBatch, error) {
	// 生成Super項目明細
	if hasSuper {
		var superItem model.DocumentItem
		superItem, err := s.generateInvoiceSuperItem(req.SuperRate, items)
		if err != nil {
			return InvoiceBatch{}, err
		}
		items = append(items, superItem)
	}
	// 生成佣金項目明細
	commissionRate, err := s.GetJobCommissionRate(req.CommissionRates, beginTimeDayStr)
	if err != nil {
		return InvoiceBatch{}, err
	}
	commissionItem, err := s.generateInvoiceCommissionItem(commissionRate, items)
	if err != nil {
		return InvoiceBatch{}, err
	}
	items = append(items, commissionItem)
	// 計算發票金額
	s.calculateInvoiceAmount(&invoice, items)
	invoice.Particular = s.generateInvoiceParticulars(items)
	return InvoiceBatch{Invoice: invoice, Items: items}, nil
}

// 生成工資項目明細
func (s *invoiceService) generateInvoiceWagesItemParticulars(PositionProfessionName string, jobShift model.JobShift, tz *time.Location) (string, error) {
	beginTime := jobShift.BeginTime.In(tz)
	beginTimeDayStr := beginTime.Format(xtool.DateDayC)
	beginTimeStr := beginTime.Format(xtool.TimeHourG1)
	endTime := jobShift.EndTime.In(tz)
	endTimeStr := endTime.Format(xtool.TimeHourG1)
	tzStr := ConfirmationNoteService.formatTimeZoneString(beginTime, tz)
	hourlyRate := ConfirmationNoteService.CustomerHumanizeAmount(jobShift.HourlyRate, 2)
	total := jobShift.PayHours.Mul(jobShift.HourlyRate).Round(2)
	totalStr := ConfirmationNoteService.CustomerHumanizeAmount(total, 2)
	particular := fmt.Sprintf("Wages (%s,%s %s-%s %s, $%s/h, $%s)", PositionProfessionName, beginTimeDayStr, beginTimeStr, endTimeStr, tzStr, hourlyRate, totalStr)
	return particular, nil
}

// 生成佣金項目明細
func (s *invoiceService) generateInvoiceCommissionItem(commissionRate decimal.Decimal, items []model.DocumentItem) (model.DocumentItem, error) {

	totalAmount := decimal.Zero
	for _, item := range items {
		totalAmount = totalAmount.Add(item.TotalAmount)
	}
	commissionAmount := totalAmount.Mul(commissionRate).Round(2)

	item := model.DocumentItem{
		ItemType:    model.DocumentItemTypeCommissionAmount,
		ItemDate:    xtype.NewNullDate(),
		Particular:  "Commission",
		TotalAmount: commissionAmount,
		TaxAmount:   decimal.Zero,
		CountTax:    "N",
		Seq:         int32(len(items) + 1),
	}
	return item, nil
}

// 生成Super項目明細
func (s *invoiceService) generateInvoiceSuperItem(superRate decimal.Decimal, items []model.DocumentItem) (model.DocumentItem, error) {
	totalAmount := decimal.Zero
	for _, item := range items {
		totalAmount = totalAmount.Add(item.TotalAmount)
	}
	superAmount := totalAmount.Mul(superRate).Round(2)
	return model.DocumentItem{
		ItemType:    model.DocumentItemTypeSuperAmount,
		ItemDate:    xtype.NewNullDate(),
		Particular:  "Superannuation",
		TotalAmount: superAmount,
		TaxAmount:   decimal.Zero,
		CountTax:    "N",
		Seq:         int32(len(items) + 1),
	}, nil
}

// 生成發票明細
func (s *invoiceService) generateInvoiceParticulars(items []model.DocumentItem) string {
	particulars := make([]string, 0)
	for _, item := range items {
		particulars = append(particulars, item.Particular)
	}
	return strings.Join(particulars, "\n")
}

// 計算發票金額
func (s *invoiceService) calculateInvoiceAmount(invoice *model.Document, items []model.DocumentItem) {
	totalAmount := decimal.Zero
	taxAmount := decimal.Zero
	commissionAmount := decimal.Zero
	grandTotal := decimal.Zero

	// 計算所有項目的總金額和稅額
	for _, item := range items {
		totalAmount = totalAmount.Add(item.TotalAmount) // 累計總金額
		taxAmount = taxAmount.Add(item.TaxAmount)       // 累計稅金
		// 計算佣金金額
		if item.ItemType == model.DocumentItemTypeCommissionAmount {
			commissionAmount = commissionAmount.Add(item.TotalAmount)
		}
		// 計算總計金額 = 項目金額 + 稅金
		grandTotal = grandTotal.Add(item.TotalAmount).Add(item.TaxAmount)
	}

	invoice.TotalAmount = totalAmount
	invoice.TaxAmount = taxAmount
	invoice.CommissionAmount = commissionAmount
	invoice.GrandTotal = grandTotal
}

// endregion ---------------------------------------------------- 代付機構 - 機構生成發票給機構 ----------------------------------------------------

// region ---------------------------------------------------- 工作 - 佣金比 ----------------------------------------------------
type JobCommissionRate struct {
	BeginTime      xtype.Date
	EndTime        xtype.Date
	CommissionRate decimal.Decimal
}

func (s *invoiceService) GetJobCommissionRate(rates []JobCommissionRate, date string) (decimal.Decimal, error) {

	for _, rate := range rates {
		if date >= rate.BeginTime.String() && date <= rate.EndTime.String() {
			return rate.CommissionRate, nil
		}
	}
	return decimal.Zero, nil
}

// 獲取某個工作的佣金比
func (s *invoiceService) getCommissionRate(db *gorm.DB, jobId uint64) ([]JobCommissionRate, error) {
	var commissionRates []JobCommissionRate
	if err := db.Table("facility_agreement as fa").
		Joins("JOIN job as j ON j.id = ? AND fa.facility_id = j.facility_id", jobId).
		Joins("JOIN facility_profile as fp ON j.facility_profile_id = fp.id").
		Joins("JOIN commission as c_u ON c_u.id = fa.pay_upfront_commission_id").
		Joins("JOIN commission as c_i ON c_i.id = fa.pay_in_arrears_commission_id").
		Select([]string{
			"fa.begin_time",
			"fa.end_time",
			fmt.Sprintf("CASE WHEN fp.payment_terms = '%s' THEN c_u.commission_rate ELSE c_i.commission_rate END AS commission_rate", model.FacilityProfilePaymentTermsPayUpfront),
		}).
		Where("j.begin_time < DATE_ADD(fa.end_time, INTERVAL 1 DAY)").
		Where("j.end_time >= fa.begin_time").
		Order("fa.begin_time").
		Find(&commissionRates).Error; err != nil {
		return nil, err
	}

	return commissionRates, nil
}

// endregion ---------------------------------------------------- 工作 - 佣金比 ----------------------------------------------------

// region ---------------------------------------------------- 發送預付發票 ----------------------------------------------------

type SendPayUpfrontInvoiceReq struct {
	DocumentId uint64 `json:"documentId"` // 發票Id
}

func (s *invoiceService) SendPayUpfrontInvoice(db *gorm.DB, req SendPayUpfrontInvoiceReq) error {
	var err error
	var document model.Document
	// 查詢發票&鎖定
	if err = db.
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", req.DocumentId).
		First(&document).Error; err != nil {
		return err
	}
	nowTime := time.Now().UTC().Truncate(time.Second)
	// 生成發票編號
	var documentNo string
	documentNo, document.SeqNo, err = s.GenerateDocumentNo(db, document.Category, document.DataType, nowTime)
	document.DocumentNo = xtype.NotNullString(documentNo)
	if err != nil {
		return err
	}
	document.SentTime = &nowTime

	// 更新發票狀態
	document.Progress = model.DocumentProgressConfirm
	updateMap := map[string]interface{}{
		"document_no": documentNo,
		"seq_no":      document.SeqNo,
		"sent_time":   &nowTime,
		"progress":    model.DocumentProgressConfirm,
	}
	if err = db.
		Model(&document).
		Where("id = ?", document.Id).
		Updates(updateMap).Error; err != nil {
		return err
	}

	// 發送發票
	// TODO: 發送發票

	return nil
}

// endregion ---------------------------------------------------- 發送預付發票 ----------------------------------------------------
