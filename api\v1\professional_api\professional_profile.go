package professional_api

import (
	"strings"

	v1 "github.com/Norray/medic-crew/api/v1"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xapp"
	"github.com/Norray/xrocket/xgorm"
	"github.com/gin-gonic/gin"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"gorm.io/gorm"
)

type ProfessionalProfileController struct {
	v1.CommonController
}

func NewProfessionalProfileController() ProfessionalProfileController {
	return ProfessionalProfileController{}
}

// @Tags Professional Profile
// @Summary 初始化專業人士資料
// @Description
// @Router /v1/professional/professional-profiles/actions/init [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileInitReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) Init(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileInitReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 取得專業人士ID
		if req.ProfessionalId, err = con.GetUserDraftProfessionalId(nc, db); err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		// 檢查是否已初始化
		var checkMsg []string
		checker := xapp.NewCK(c)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckAlreadyInit(db, &model.Professional{}, req.ProfessionalId)
			})
		con.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalProfession, req.Profession)
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.ProfessionalProfileService.Init(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 查詢專業人士資料
// @Description
// @Router /v1/professional/professional-profiles/actions/inquire [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileInquireReq true "parameter"
// @Success 200 {object} services.ProfessionalProfileInquireResp
func (con ProfessionalProfileController) Inquire(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileInquireReq
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &model.Professional{}, req.ProfessionalId, nc.GetJWTUserId())
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}
		resp, err := services.ProfessionalProfileService.Inquire(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 編輯專業人士資料
// @Description
// @Router /v1/professional/professional-profiles/actions/edit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileEditReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) Edit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileEditReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)

		// 取得專業人士ID
		if req.ProfessionalId, err = con.GetUserDraftProfessionalId(nc, db); err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		req.UserId = nc.GetJWTUserId()

		// 檢查是否已初始化
		var checkMsg []string
		var professional model.Professional
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckCanEdit(professional)
			})
		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		// 缺少必要的參數
		missing := i18n.Message{
			ID:    "checker.professional_file.edit.missing_parameter",
			Other: "Some thing went wrong, please try again later.",
		}
		switch req.EditType {
		case services.ProfessionalProfileEditTypePersonalInformation:
			if req.PersonalInformation == nil {
				checker.Run(func() (bool, i18n.Message, error) {
					return false, missing, nil
				})
				break
			}
			con.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalPermissionToWork, req.PersonalInformation.PermissionToWork)
			con.CheckSelectionExist(checker, db, model.SelectionTypeProfessionalProfession, req.PersonalInformation.Profession)
			con.CheckSelectionsExist(checker, db, model.SelectionTypeGender, req.PersonalInformation.Gender)
			con.CheckSelectionsExist(checker, db, model.SelectionTypeRelationship, req.PersonalInformation.EmergencyContactRelationship)
			con.CheckSelectionsExist(checker, db, model.SelectionTypeLanguage, req.PersonalInformation.Language)
			if req.PersonalInformation.PhotoFileId != 0 {
				checker.
					Run(func() (bool, i18n.Message, error) {
						return services.ProfessionalFileService.CheckFileExist(db, professional.UserId, []uint64{req.PersonalInformation.PhotoFileId}, []string{model.ProfessionalFileCodePhoto})
					})
			}
		case services.ProfessionalProfileEditTypeWorkPreferencesAndExperience:
			if req.WorkPreferencesAndExperience == nil {
				checker.Run(func() (bool, i18n.Message, error) {
					return false, missing, nil
				})
				break
			}
			preferredSpecialities := services.ProfessionalProfileService.GetPreferredSpecialities(professional.Profession, req.WorkPreferencesAndExperience.PreferredSpecialities)
			if len(preferredSpecialities) > 0 {
				checker.Run(func() (bool, i18n.Message, error) {
					return services.ProfessionalProfileService.CheckPreferredSpecialities(db, professional.Profession, strings.Join(preferredSpecialities, ","))
				})
			}

			switch professional.Profession {
			case model.ProfessionalProfessionMedicalPractitioner:
				con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelMedicalPractitioner, req.WorkPreferencesAndExperience.ExperienceLevel)
				if req.WorkPreferencesAndExperience.ExperienceLevel != "" && req.WorkPreferencesAndExperience.GraduationYear > 0 {
					checker.Run(func() (bool, i18n.Message, error) {
						return services.ProfessionalProfileService.CheckMedicalPractitionerExperienceLevel(db, req.WorkPreferencesAndExperience.ExperienceLevel, int(req.WorkPreferencesAndExperience.GraduationYear))
					})
				}
				// 校驗Medical Practitioner的Preferred Grade是否符合Experience Level要求
				if req.WorkPreferencesAndExperience.ExperienceLevel != "" {
					for _, speciality := range req.WorkPreferencesAndExperience.PreferredSpecialities {
						if speciality.Grade == "" {
							continue
						}
						checker.Run(func() (bool, i18n.Message, error) {
							return services.ProfessionalProfileService.CheckMedicalPractitionerPreferredGrade(req.WorkPreferencesAndExperience.ExperienceLevel, speciality.Grade)
						})
					}
				}
			case model.ProfessionalProfessionEnrolledNurse, model.ProfessionalProfessionPersonalCareWorker:
				con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelGeneral, req.WorkPreferencesAndExperience.ExperienceLevel)
			case model.ProfessionalProfessionRegisteredNurse:
				con.CheckSelectionsExist(checker, db, model.SelectionTypeExperienceLevelRegisteredNurse, req.WorkPreferencesAndExperience.ExperienceLevel)
			}
			checkerFileCodes := []string{
				model.ProfessionalFileCodeQualificationCertificate,     // 學歷證書
				model.ProfessionalFileCodeCurriculumVitae,              // 專業人士履歷
				model.ProfessionalFileCodeRegistrarAccreditedEnrolment, // Registrar (Accredited) 入學證明
				model.ProfessionalFileCodeFellowshipCertificate,        // Fellowship證書
				model.ProfessionalFileCodeSpecialistQualification,      // Specialist資格證明
			}
			// 找到相關文件Code
			var personalCareWorkerQualificationCode []string
			if model.ProfessionalProfessionPersonalCareWorker == professional.Profession {
				personalCareWorkerQualificationCode, err = services.SelectionService.GetCodeByType(db, model.ProfessionalFileCodePersonalCareWorkQualification)
				if err != nil {
					nc.ErrorResponse(req, err)
					return
				}
				checkerFileCodes = append(checkerFileCodes, personalCareWorkerQualificationCode...)
			}
			// 檢查文件是不是個人護理工作資格
			for _, files := range req.WorkPreferencesAndExperience.Files {
				if len(files) == 0 {
					continue
				}
				checker.
					Run(func() (bool, i18n.Message, error) {
						var professionalFileIds []uint64
						for _, file := range files {
							professionalFileIds = append(professionalFileIds, file.ProfessionalFileIds...)
						}
						return services.ProfessionalFileService.CheckFileExist(db, professional.UserId, professionalFileIds, checkerFileCodes)
					})
			}
			// 檢查經驗
			if len(req.WorkPreferencesAndExperience.Experiences) > 0 {
				specialities := make([]string, 0)
				grades := make([]string, 0)
				for _, experience := range req.WorkPreferencesAndExperience.Experiences {
					speciality := experience.Speciality
					if professional.Profession == model.ProfessionalProfessionMedicalPractitioner {
						speciality = experience.SubSpeciality
					}
					if speciality != "" {
						specialities = append(specialities, strings.Split(speciality, ",")...)
					}
					if experience.Grade != "" {
						grades = append(grades, experience.Grade)
					}
				}
				if len(specialities) > 0 {
					specialitiesStr := strings.Join(specialities, ",")
					checker.Run(func() (bool, i18n.Message, error) {
						return services.ProfessionalProfileService.CheckPreferredSpecialities(db, professional.Profession, specialitiesStr)
					})
				}
				if professional.Profession == model.ProfessionalProfessionMedicalPractitioner && len(grades) > 0 {
					gradesStr := strings.Join(grades, ",")
					con.CheckSelectionsExist(checker, db, model.SelectionTypePreferredGrade, gradesStr)
				} else {
					roles := make([]string, 0)
					for _, role := range req.WorkPreferencesAndExperience.Experiences {
						if role.Role != "" && role.Role != model.ProfessionalExperienceRoleOther {
							roles = append(roles, role.Role)
						}
					}
					if len(roles) > 0 {
						rolesStr := strings.Join(roles, ",")
						con.CheckSelectionsExist(checker, db, model.SelectionTypeProfessionalProfession, rolesStr)
					}
				}
			}
		case services.ProfessionalProfileEditTypeRegistrationAndCertification:
			if req.RegistrationAndCertification == nil {
				checker.Run(func() (bool, i18n.Message, error) {
					return false, missing, nil
				})
				break
			}
			checker.
				Run(func() (bool, i18n.Message, error) {
					files := req.RegistrationAndCertification.Files[model.ProfessionalFileCodeAbn]
					if len(files) == 0 {
						return true, i18n.Message{}, nil
					}
					for _, file := range files {
						if file.Number != "" && file.Number != professional.AbnNumber {
							// 只有ABN變更，就重新查信息，並更新到數據庫
							_, _, err := services.ProfessionalProfileService.CheckAbn(db, file.Number, &req.RegistrationAndCertification.AbnInfo, req.RegistrationAndCertification.AbnResp)
							if err != nil {
								return false, i18n.Message{}, err
							}
						}
					}
					return true, i18n.Message{}, nil
				})
			con.CheckMultipleFilesExist(checker, db, professional, []string{
				model.ProfessionalFileCodeAbn,              // ABN
				model.ProfessionalFileCodeAhpraCertificate, // AHPRA證書
			}, req.RegistrationAndCertification.Files)
		case services.ProfessionalProfileEditTypeProofOfIdentityAndRecords:
			if req.ProofOfIdentityAndRecords == nil {
				checker.Run(func() (bool, i18n.Message, error) {
					return false, missing, nil
				})
				break
			}
			con.CheckMultipleFilesExist(checker, db, professional, []string{
				model.ProfessionalFileCodeAustralianPassport,                    // 澳洲護照
				model.ProfessionalFileCodeForeignPassport,                       // 外國護照
				model.ProfessionalFileCodeAustralianBirthCertificate,            // 澳洲出生證明
				model.ProfessionalFileCodeAustralianCitizenshipCertificate,      // 澳洲公民證
				model.ProfessionalFileCodeCurrentAustraliaDriverLicence,         // 澳洲駕照
				model.ProfessionalFileCodeAustralianPublicServiceEmployeeIDCard, // 澳洲公務員ID卡
				model.ProfessionalFileCodeOtherAustralianGovernmentIssueIDCard,  // 其他澳洲政府發出的ID卡
				model.ProfessionalFileCodeTertiaryStudentIDCard,                 // 大學生ID卡
				model.ProfessionalFileCodeCreditDebitAtmCard,                    // 信用卡/扣帳卡/ATM卡
				model.ProfessionalFileCodeMedicareCard,                          // 醫療卡
				model.ProfessionalFileCodeUtilityBillOrRateNotice,               // 水電費單或收費通知
				model.ProfessionalFileCodeStatementFromFinancialInstitution,     // 金融機構的結單
				model.ProfessionalFileCodeCentrelinkOrPensionCard,               // 澳洲国民福利署或養老金卡
				model.ProfessionalFileCodeVisa,                                  // 簽證
				model.ProfessionalFileCodeNationalCriminalCheck,                 // 國家犯罪檢查
				model.ProfessionalFileCodeWorkingWithChildrenOrVulnerablePeople, // 兒童/脆弱人群工作檢查
				model.ProfessionalFileCodeCurrentImmunisationRecords,            // 現在的免疫記錄
				model.ProfessionalFileCodeCommonwealthStatutoryDeclaration,      // 聯邦法定聲明
			}, req.ProofOfIdentityAndRecords.Files)
		case services.ProfessionalProfileEditTypeAdditionalCertification:
			if req.AdditionalInformation == nil {
				checker.Run(func() (bool, i18n.Message, error) {
					return false, missing, nil
				})
				break
			}
			con.CheckSingleFileExist(checker, db, professional, model.ProfessionalFileCodeSignedAgreement, req.AdditionalInformation.Files[model.ProfessionalFileCodeSignedAgreement])
		}

		checkMsg, err = checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}

		tx := db.Begin()
		err = services.ProfessionalProfileService.ProfessionalProfileEdit(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)

	} else {
		nc.BadRequestResponse(err)
	}
}

// 檢查單一選項是否存在
func (con ProfessionalProfileController) CheckSelectionExist(checker *xapp.XChecker, db *gorm.DB, selectionType string, selectionValue string) {
	checker.
		Run(func() (bool, i18n.Message, error) {
			if selectionValue == "" {
				return true, i18n.Message{}, nil
			}
			return services.SelectionService.CheckSelectionExist(db, &model.Selection{}, selectionType, selectionValue, model.SelectionStatusEnable)
		})
}

// 檢查多個選項是否存在
func (con ProfessionalProfileController) CheckSelectionsExist(checker *xapp.XChecker, db *gorm.DB, selectionType string, selectionValues string) {
	checker.
		Run(func() (bool, i18n.Message, error) {
			if selectionValues == "" {
				return true, i18n.Message{}, nil
			}
			return services.SelectionService.CheckSelectionsExist(db, selectionType, selectionValues, model.SelectionStatusEnable)
		})
}

var missingParameter = i18n.Message{
	ID:    "checker.professional_file.edit.missing_parameter",
	Other: "Some thing went wrong, please try again later.",
}

// 檢查單一文件是否存在
func (con ProfessionalProfileController) CheckSingleFileExist(checker *xapp.XChecker, db *gorm.DB, professional model.Professional, fileCode string, files []model.ProfessionalProfileFile) {
	if len(files) == 0 {
		return
	}
	for _, file := range files {
		if file.FileCode == "" {
			checker.Run(func() (bool, i18n.Message, error) {
				return false, missingParameter, nil
			})
			continue
		}
		if file.FileCode == fileCode && len(file.ProfessionalFileIds) > 0 {
			checker.
				Run(func() (bool, i18n.Message, error) {
					return services.ProfessionalFileService.CheckFileExist(db, professional.UserId, file.ProfessionalFileIds, []string{fileCode})
				})
		}
	}
}

// 檢查多種FileCode文件是否存在
func (con ProfessionalProfileController) CheckMultipleFilesExist(checker *xapp.XChecker, db *gorm.DB, professional model.Professional, fileCodes []string, files map[string][]model.ProfessionalProfileFile) {
	for _, fileCode := range fileCodes {
		if len(files) == 0 {
			return
		}
		if _, ok := files[fileCode]; !ok {
			continue
		}
		for _, file := range files[fileCode] {
			if file.FileCode == "" {
				checker.Run(func() (bool, i18n.Message, error) {
					return false, missingParameter, nil
				})
				continue
			}
			if file.FileCode == fileCode && len(file.ProfessionalFileIds) > 0 {
				checker.
					Run(func() (bool, i18n.Message, error) {
						return services.ProfessionalFileService.CheckFileExist(db, professional.UserId, file.ProfessionalFileIds, []string{fileCode})
					})
			}
		}
	}
}

// @Tags Professional Profile
// @Summary 查詢專業人士資料進度
// @Description
// @Router /v1/professional/professional-profiles/actions/progress [GET]
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} services.ProfessionalProfileProgress
func (con ProfessionalProfileController) Progress(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileProgressReq
	var err error
	db := xgorm.DB.WithContext(c)
	// 取得專業人士ID
	if req.ProfessionalId, err = con.GetUserDraftProfessionalId(nc, db); err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	resp, err := services.ProfessionalProfileService.ProfessionalProfileProgress(db, req)
	if err != nil {
		nc.ErrorResponse(req, err)
		return
	}
	nc.OKResponse(resp)
}

// @Tags Professional Profile
// @Summary 提交專業人士資料
// @Description
// @Router /v1/professional/professional-profiles/actions/submit [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileSubmitReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) Submit(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileSubmitReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 取得專業人士ID
		if req.ProfessionalId, err = con.GetUserDraftProfessionalId(nc, db); err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		// 提示checker
		var professional model.Professional
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdCheckFileTypes(professional)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckWorkingWithChildrenOrVulnerablePeopleStates(professional)
			})
		checkMsg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(checkMsg) > 0 {
			nc.BadRequestResponseWithCheckMsg(checkMsg)
			return
		}
		// 提示checker
		alertChecker := xapp.NewCK(c, true)
		alertChecker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckCanSubmit(db, professional, req.UpdatePrompt, req.OnlyCheck == "Y")
			})
		alertMsg, err := alertChecker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(alertMsg) > 0 {
			nc.AlertResponse(alertMsg)
			return
		}
		// 檢查文件過期日期
		ok, alertMsgStr, err := services.ProfessionalProfileService.CheckFileExpiration(nc.GetLanguage(), professional)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if !ok {
			nc.SingleAlertResponse(alertMsgStr)
			return
		}
		// 只檢查參數不提交，返回成功
		if req.OnlyCheck == "Y" {
			nc.OKResponse(nil)
			return
		}

		tx := db.Begin()
		err = services.ProfessionalProfileService.ProfessionalProfileSubmit(tx, req, professional)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()

		// 發送通知電郵
		err = services.ProfessionalReferenceFormService.SendNotificationMailToAll(db, nc.GetLanguage(), req.ProfessionalId)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}

		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 撤銷專業人士資料
// @Description
// @Router /v1/professional/professional-profiles/actions/withdraw [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileWithdrawReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) Withdraw(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileWithdrawReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		// 取得專業人士ID
		if req.ProfessionalId, err = con.GetUserDraftProfessionalId(nc, db); err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		// 提示checker
		var professional model.Professional
		alert := xapp.NewCK(c, true)
		alert.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &professional, req.ProfessionalId)
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckCanWithdraw(professional)
			})
		msg, err := alert.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}
		tx := db.Begin()
		err = services.ProfessionalProfileService.ProfessionalProfileWithdraw(tx, req, professional)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 查詢ABN是否有效
// @Description
// @Router /v1/professional/professional-profiles/actions/check-abn [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileCheckAbnReq true "parameter"
// @Success 200 {object} services.ProfessionalProfileCheckAbnResp
func (con ProfessionalProfileController) CheckAbn(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileCheckAbnReq
	if err := c.ShouldBindQuery(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		resp, err := services.ProfessionalProfileService.ProfessionalProfileCheckAbn(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 查詢付款資料
// @Description
// @Router /v1/professional/professional-profiles/actions/payment-detail [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfilePaymentDetailReq true "parameter"
// @Success 200 {object} services.ProfessionalProfilePaymentDetailResp
func (con ProfessionalProfileController) PaymentDetail(c *gin.Context) {
	var req services.ProfessionalProfilePaymentDetailReq
	nc := xapp.NGinCtx{C: c}
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()
		resp, err := services.ProfessionalProfileService.ProfessionalProfilePaymentDetail(db, req)
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 更新付款資料
// @Description
// @Router /v1/professional/professional-profiles/actions/update-payment-detail [POST]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileUpdatePaymentDetailReq true "parameter"
// @Success 200 "Success"
func (con ProfessionalProfileController) UpdatePaymentDetail(c *gin.Context) {
	nc := xapp.NGinCtx{C: c}
	var req services.ProfessionalProfileUpdatePaymentDetailReq
	if err := c.ShouldBindJSON(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		req.UserId = nc.GetJWTUserId()
		tx := db.Begin()
		err = services.ProfessionalProfileService.ProfessionalProfileUpdatePaymentDetail(tx, req)
		if err != nil {
			tx.Rollback()
			nc.ErrorResponse(req, err)
			return
		}
		tx.Commit()
		nc.OKResponse(nil)
	} else {
		nc.BadRequestResponse(err)
	}
}

// @Tags Professional Profile
// @Summary 專業人士資料對比
// @Description
// @Router /v1/professional/professional-profiles/actions/compare [GET]
// @Produce json
// @Security ApiKeyAuth
// @Param json body services.ProfessionalProfileCompareReq true "parameter"
// @Success 200 {object} services.ProfessionalProfileCompareResp
func (con ProfessionalProfileController) Compare(c *gin.Context) {
	var req services.ProfessionalProfileCompareReq
	nc := xapp.NGinCtx{C: c}
	if err := c.ShouldBind(&req); err == nil {
		db := xgorm.DB.WithContext(c)
		checker := xapp.NewCK(c, true)
		checker.
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &model.Professional{}, req.CurrentProfessionalId, nc.GetJWTUserId())
			}).
			Run(func() (bool, i18n.Message, error) {
				return services.ProfessionalProfileService.CheckIdExist(db, &model.Professional{}, req.NewProfessionalId, nc.GetJWTUserId())
			})
		msg, err := checker.Result()
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		if len(msg) > 0 {
			nc.BadRequestResponseWithCheckMsg(msg)
			return
		}
		resp, err := services.ProfessionalProfileService.Compare(db, req, nc.GetLanguage())
		if err != nil {
			nc.ErrorResponse(req, err)
			return
		}
		nc.OKResponse(resp)
	} else {
		nc.BadRequestResponse(err)
	}
}
