package job

import (
	"context"
	"testing"
	"time"

	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xconfig"
	"github.com/Norray/xrocket/xgorm"
	"github.com/Norray/xrocket/xredis"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 測試環境初始化
func setupTestEnvironment(t *testing.T) *gorm.DB {
	xconfig.Setup("../../config/app.ini")
	xredis.DefaultSetup()
	xgorm.DefaultSetup()

	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	return xgorm.DB.WithContext(ctx)
}

// 測試24小時提醒功能
func TestCheck24HourReminders(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.With<PERSON>ield("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Professional 24 Hour Reminders", func(t *testing.T) {
		// 測試專業人員24小時提醒
		check24HourReminders(db, nowTime, logger)
		t.Log("Professional 24 hour reminders test completed")
	})

	t.Run("Facility 24 Hour Reminders", func(t *testing.T) {
		// 測試機構24小時提醒
		check24HourReminders(db, nowTime, logger)
		t.Log("Facility 24 hour reminders test completed")
	})
}

// 測試2小時提醒功能
func TestCheck2HourReminders(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Professional 2 Hour Reminders", func(t *testing.T) {
		// 測試專業人員2小時提醒
		check2HourReminders(db, nowTime, logger)
		t.Log("Professional 2 hour reminders test completed")
	})
}

// 測試無申請2小時提醒功能
func TestCheckNoApplication2Hour(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("No Application 2 Hour Reminders", func(t *testing.T) {
		// 測試無申請2小時提醒
		checkNoApplication2Hour(db, nowTime, logger)
		t.Log("No application 2 hour reminders test completed")
	})
}

// 測試自動取消工作功能
func TestCheckAutoCancel(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	// 使用當前時間進行測試
	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Auto Cancel Jobs", func(t *testing.T) {
		// 測試自動取消工作
		checkAutoCancel1Hour(db, nowTime, logger)
		t.Log("Auto cancel jobs test completed")
	})
}

// 測試完整的工作提醒檢查流程
func TestJobCheckJobReminders(t *testing.T) {
	t.Run("Full Job Reminders Check", func(t *testing.T) {
		// 執行完整的工作提醒檢查
		jobCheckJobReminders()
		t.Log("Full job reminders check test completed")
	})
}

// 測試防重複機制
func TestDuplicatePreventionMechanism(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Duplicate Prevention Test", func(t *testing.T) {
		// 第一次執行
		t.Log("First execution - should process reminders")
		check24HourReminders(db, nowTime, logger)

		// 第二次執行 - 應該跳過已處理的提醒
		t.Log("Second execution - should skip already processed reminders")
		check24HourReminders(db, nowTime, logger)

		t.Log("Duplicate prevention test completed")
	})
}

// 測試時間窗口邏輯
func TestTimeWindowLogic(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	t.Run("24 Hour Window Test", func(t *testing.T) {
		// 測試24小時時間窗口
		testTime := time.Now().UTC().Add(24 * time.Hour)
		check24HourReminders(db, testTime, logger)
		t.Log("24 hour window test completed")
	})

	t.Run("2 Hour Window Test", func(t *testing.T) {
		// 測試2小時時間窗口
		testTime := time.Now().UTC().Add(2 * time.Hour)
		check2HourReminders(db, testTime, logger)
		checkNoApplication2Hour(db, testTime, logger)
		t.Log("2 hour window test completed")
	})
}

// 測試批量通知服務
func TestBatchNotificationServices(t *testing.T) {
	db := setupTestEnvironment(t)

	// 創建測試用的JobReminderInfo
	testReminders := []services.JobReminderInfo{
		{
			JobId:            1,
			JobApplicationId: 1,
			FacilityId:       1,
			JobTitle:         "Test Job",
			BeginTime:        time.Now().UTC().Add(24 * time.Hour),
			UserId:           1,
			ReminderType:     services.ReminderTypeProfessional24Hour,
		},
	}

	t.Run("Professional 24 Hour Batch", func(t *testing.T) {
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Professional 24 hour batch test completed with expected result: %v", err)
		} else {
			t.Log("Professional 24 hour batch test completed successfully")
		}
	})

	t.Run("Professional 2 Hour Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeProfessional2Hour
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar2HourBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Professional 2 hour batch test completed with expected result: %v", err)
		} else {
			t.Log("Professional 2 hour batch test completed successfully")
		}
	})

	t.Run("Facility 24 Hour Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeFacility24Hour
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityCalendar24HourBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Facility 24 hour batch test completed with expected result: %v", err)
		} else {
			t.Log("Facility 24 hour batch test completed successfully")
		}
	})

	t.Run("No Application Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeNoApplication2Hour
		testReminders[0].JobApplicationId = 0 // 無申請提醒不需要申請ID
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityJobNoApplicationBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("No application batch test completed with expected result: %v", err)
		} else {
			t.Log("No application batch test completed successfully")
		}
	})

	t.Run("Auto Cancel Batch", func(t *testing.T) {
		testReminders[0].ReminderType = services.ReminderTypeAutoCancel1Hour
		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateFacilityJobAutoCancelBatch(tx, testReminders)
		})
		if err != nil {
			t.Logf("Auto cancel batch test completed with expected result: %v", err)
		} else {
			t.Log("Auto cancel batch test completed successfully")
		}
	})
}

// 測試錯誤處理
func TestErrorHandling(t *testing.T) {
	db := setupTestEnvironment(t)

	t.Run("Empty Reminders List", func(t *testing.T) {
		emptyReminders := []services.JobReminderInfo{}

		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, emptyReminders)
		})

		if err != nil {
			t.Errorf("Empty reminders should not cause error: %v", err)
		} else {
			t.Log("Empty reminders test passed")
		}
	})

	t.Run("Invalid Job Data", func(t *testing.T) {
		invalidReminders := []services.JobReminderInfo{
			{
				JobId:            0, // 無效的JobId
				JobApplicationId: 0,
				FacilityId:       0,
				JobTitle:         "",
				BeginTime:        time.Time{}, // 零值時間
				UserId:           0,
				ReminderType:     services.ReminderTypeProfessional24Hour,
			},
		}

		err := db.Transaction(func(tx *gorm.DB) error {
			return services.SystemNotificationService.CreateProfessionalCalendar24HourBatch(tx, invalidReminders)
		})

		// 預期會有錯誤，因為數據無效
		if err != nil {
			t.Logf("Invalid data test completed with expected error: %v", err)
		} else {
			t.Log("Invalid data test completed without error")
		}
	})
}

// 測試並發安全性
func TestConcurrencySafety(t *testing.T) {
	db := setupTestEnvironment(t)

	traceId := uuid.NewV4().String()
	logger := log.WithField("traceId", traceId).WithField("task", CronCheckJobReminders)

	nowTime := time.Now().UTC().Truncate(time.Second)

	t.Run("Concurrent Execution", func(t *testing.T) {
		// 模擬並發執行
		done := make(chan bool, 2)

		go func() {
			check24HourReminders(db, nowTime, logger)
			done <- true
		}()

		go func() {
			check2HourReminders(db, nowTime, logger)
			done <- true
		}()

		// 等待兩個goroutine完成
		<-done
		<-done

		t.Log("Concurrent execution test completed")
	})
}
