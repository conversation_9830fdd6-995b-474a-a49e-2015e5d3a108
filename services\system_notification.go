package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/Norray/xrocket/xapp/xresp"
	"github.com/Norray/xrocket/xmodel"
	"github.com/shopspring/decimal"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"github.com/Norray/medic-crew/model"
)

var SystemNotificationService = new(systemNotificationService)

// JobReminderInfo 工作提醒信息結構
type JobReminderInfo struct {
	UserId           uint64    // 專業人員ID
	FacilityId       uint64    // 機構ID
	JobId            uint64    // 工作ID
	JobApplicationId uint64    // 工作申請ID
	JobTitle         string    // 工作標題
	BeginTime        time.Time // 工作開始時間
	ReminderType     string    // 使用 ReminderType* 常數
}

// Job Reminder Type 常量
const (
	// Professional Reminder Types
	ReminderTypeProfessional24Hour = "24_HOUR_PROFESSIONAL" // 專業人員24小時提醒
	ReminderTypeProfessional2Hour  = "2_HOUR_PROFESSIONAL"  // 專業人員2小時提醒

	// Facility Reminder Types
	ReminderTypeFacility24Hour     = "24_HOUR_FACILITY"      // 機構24小時提醒
	ReminderTypeNoApplication2Hour = "NO_APPLICATION_2_HOUR" // 工作開始前2小時無人申請
	ReminderTypeAutoCancel1Hour    = "AUTO_CANCEL_1_HOUR"    // 工作開始前1小時無人申請自動取消

	// Job Completion Reminder Types
	ReminderTypeJobCompletionProfessional = "JOB_COMPLETION_PROFESSIONAL" // 工作完成專業人士提醒
)

// 通知文字常量
const (
	// Professional Job 通知文字
	MsgProfessionalJobNewAvailableTitle   = "New Job Available"
	MsgProfessionalJobNewAvailableContent = "A new suitable job is available for you. Please have a look."

	MsgProfessionalJobInvitationTitle   = "New Job Invitation"
	MsgProfessionalJobInvitationContent = "You have received a new job invitation. Please take a look and respond."

	MsgProfessionalJobAcceptedTitle   = "Job Invitation Accepted"
	MsgProfessionalJobAcceptedContent = "The job invitation has been accepted and added to your calendar."

	MsgProfessionalJobCancelledTitle   = "Job Cancelled"
	MsgProfessionalJobCancelledContent = "This job has been cancelled. Please take note and adjust your schedule accordingly."

	MsgProfessionalJobCompensationTitle   = "Job Cancellation Compensation"
	MsgProfessionalJobCompensationContent = "The job was cancelled, and a compensation invoice has been generated. Please review and confirm."

	// Professional Calendar 通知文字
	MsgProfessionalCalendar24HourTitle   = "Upcoming Shift Reminder"
	MsgProfessionalCalendar24HourContent = "You have a job in 24 hours. Please take note of the time."

	MsgProfessionalCalendar2HourTitle   = "Shift Starting Soon"
	MsgProfessionalCalendar2HourContent = "Your shift starts in 2 hours. Please be on time."

	// Professional Billing 通知文字
	MsgProfessionalBillingConfirmationNoteGenerateTitle   = "Generate Confirmation Note"
	MsgProfessionalBillingConfirmationNoteGenerateContent = "You have completed your shift. Please generate your confirmation note."

	MsgProfessionalBillingConfirmationNoteApprovedTitle   = "Confirmation Note Approved"
	MsgProfessionalBillingConfirmationNoteApprovedContent = "Your confirmation note has been approved, and an invoice has been automatically generated for you."

	MsgProfessionalBillingConfirmationNoteRejectedTitle   = "Confirmation Note Rejected"
	MsgProfessionalBillingConfirmationNoteRejectedContent = "Your confirmation note was not approved. Please review the reason and resubmit."

	// Professional Profile 通知文字
	MsgProfessionalProfileApprovedTitle   = "Profile Approved"
	MsgProfessionalProfileApprovedContent = "Your profile has been approved. Please complete the training to be eligible for jobs."

	MsgProfessionalProfileRejectedTitle   = "Profile Rejected"
	MsgProfessionalProfileRejectedContent = "Your profile was not approved. Please review the reason and make the necessary updates."

	MsgProfessionalProfileNeedSuperTitle   = "Complete Superannuation Details"
	MsgProfessionalProfileNeedSuperContent = "Your profile has been approved. Please complete your Superannuation details."

	MsgProfessionalProfileDocumentExpireTitle   = "Document Expiring Soon"
	MsgProfessionalProfileDocumentExpireContent = "%s will expire within 30 days. Please update it promptly."

	MsgProfessionalProfileReferenceDeclinedTitle   = "Reference Declined"
	MsgProfessionalProfileReferenceDeclinedContent = "Your referee %s has declined to provide the required information. Please contact them or select another referee."

	// Facility Job 通知文字
	MsgFacilityJobNeedConfirmTitle   = "Confirm Professional Assignment"
	MsgFacilityJobNeedConfirmContent = "Only 24 hours remain until the shift starts. Please confirm the assigned professional as soon as possible."

	MsgFacilityJobNoApplicationTitle   = "No Applications Received"
	MsgFacilityJobNoApplicationContent = "Only 2 hours remain until the shift starts and no applications have been received. If no applications are received within the next hour, the job will be automatically cancelled in 1 hour."

	MsgFacilityJobAutoCancelTitle   = "Job Automatically Cancelled"
	MsgFacilityJobAutoCancelContent = "Only 1 hour remains until the shift starts and no applications were received. The job has been automatically cancelled."

	MsgFacilityJobAcceptedTitle   = "Job Invitation Accepted"
	MsgFacilityJobAcceptedContent = "The job invitation has been accepted and added to your calendar."

	MsgFacilityJobDeclineTitle   = "Job Invitation Declined"
	MsgFacilityJobDeclineContent = "The job invitation has been declined by %s. Please contact other applicants."

	MsgFacilityJobInvitationExpiredTitle   = "Job Invitation Expired"
	MsgFacilityJobInvitationExpiredContent = "The job invitation has expired without being accepted. You may contact this applicant again, or reach out to other applicants."

	MsgFacilityJobCancelledTitle   = "Job Cancelled by Professional"
	MsgFacilityJobCancelledContent = "The job has been cancelled by the professional. Please adjust your arrangements accordingly."

	// Facility Calendar 通知文字
	MsgFacilityCalendar24HourTitle   = "Upcoming Shift Reminder"
	MsgFacilityCalendar24HourContent = "Your shift starts in 24 hours. Please confirm the arrangements."

	// Facility Billing 通知文字
	MsgFacilityBillingConfirmationNoteReceivedTitle   = "New Confirmation Note"
	MsgFacilityBillingConfirmationNoteReceivedContent = "You have received a new confirmation note. Please review it."

	MsgFacilityBillingInvoiceReceivedTitle   = "New Invoice"
	MsgFacilityBillingInvoiceReceivedContent = "You have received a new invoice. Please review and process it."

	// Facility Information 通知文字
	MsgFacilityInformationApprovedTitle   = "Information Approved"
	MsgFacilityInformationApprovedContent = "Your information has been approved. You can now post new jobs."

	MsgFacilityInformationRejectedTitle   = "Information Rejected"
	MsgFacilityInformationRejectedContent = "Your information was not approved. Please review the reason and make the necessary updates."
)

// 機構每種通知對應用戶的權限
var FacilityNotificationUserPermissionCodeMap = map[string]string{
	model.SystemNotificationTypeFacilityJobAccepted:                     "facility-job-view",                       // 工作接受
	model.SystemNotificationTypeFacilityJobCancelled:                    "facility-job-view",                       // 工作取消
	model.SystemNotificationTypeFacilityJobDecline:                      "facility-job-view",                       // 工作拒絕
	model.SystemNotificationTypeFacilityJobInvitationExpired:            "facility-job-view",                       // 工作邀請過期
	model.SystemNotificationTypeFacilityJobNoApplication:                "facility-job-view",                       // 工作無申請
	model.SystemNotificationTypeFacilityJobAutoCancel:                   "facility-job-view",                       // 工作自動取消
	model.SystemNotificationTypeFacilityJobNeedConfirm:                  "facility-job-view",                       // 工作需要確認
	model.SystemNotificationTypeFacilityCalendar24Hour:                  "facility-job-view",                       // 24小時提醒
	model.SystemNotificationTypeFacilityBillingConfirmationNoteReceived: "facility-billing-confirmation-note-view", // 收到確認單
	model.SystemNotificationTypeFacilityBillingInvoiceReceived:          "facility-billing-invoice-view",           // 收到發票
	model.SystemNotificationTypeFacilityInformationApproved:             "facility-profile-view",                   // 機構信息通過
	model.SystemNotificationTypeFacilityInformationRejected:             "facility-profile-view",                   // 機構信息駁回
}

// systemNotificationService 系統通知服務
type systemNotificationService struct{}

// region ---------------------------------------------------- Professional Job Notifications 專業人員工作通知 ----------------------------------------------------

// 創建專業人員新工作可用通知請求
type CreateProfessionalJobNewAvailableReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
	StartTime    string `json:"startTime"`    // 開始時間
}

// 創建專業人員新工作可用通知
func (s *systemNotificationService) CreateProfessionalJobNewAvailable(db *gorm.DB, req CreateProfessionalJobNewAvailableReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalJobNewAvailable,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobTitle":     req.JobTitle,
		"facilityName": req.FacilityName,
		"startTime":    req.StartTime,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobNewAvailable,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalJobNewAvailableTitle,
		Content:          MsgProfessionalJobNewAvailableContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員工作邀請通知請求
type CreateProfessionalJobInvitationReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	FacilityName     string `json:"facilityName"`     // 機構名稱
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建專業人員工作邀請通知
func (s *systemNotificationService) CreateProfessionalJobInvitation(db *gorm.DB, req CreateProfessionalJobInvitationReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalJobInvitation,
		"userId":           req.UserId,
		"jobId":            req.JobId,
		"jobApplicationId": req.JobApplicationId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobInvitation,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalJobInvitationTitle,
		Content:          MsgProfessionalJobInvitationContent,
		RelatedId:        req.JobApplicationId,
		RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員工作接受通知請求
type CreateProfessionalJobAcceptedReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	FacilityId       uint64 `json:"facilityId"`       // 機構ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建專業人員工作接受通知
func (s *systemNotificationService) CreateProfessionalJobAccepted(db *gorm.DB, req CreateProfessionalJobAcceptedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalJobAccepted,
		"userId":           req.UserId,
		"jobId":            req.JobId,
		"jobApplicationId": req.JobApplicationId,
		"facilityId":       req.FacilityId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":            req.JobId,
		"jobApplicationId": req.JobApplicationId,
		"facilityId":       req.FacilityId,
		"jobTitle":         req.JobTitle,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobAccepted,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalJobAcceptedTitle,
		Content:          MsgProfessionalJobAcceptedContent,
		RelatedId:        req.JobApplicationId,
		RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員工作取消通知請求
type CreateProfessionalJobCancelledReq struct {
	UserId             uint64 `json:"userId"`             // 用戶ID
	JobId              uint64 `json:"jobId"`              // 工作ID
	JobTitle           string `json:"jobTitle"`           // 工作標題
	FacilityName       string `json:"facilityName"`       // 機構名稱
	CancellationReason string `json:"cancellationReason"` // 取消原因
	CreatorUserId      uint64 `json:"creatorUserId"`      // 創建用戶ID
}

// 創建專業人員工作取消通知
func (s *systemNotificationService) CreateProfessionalJobCancelled(db *gorm.DB, req CreateProfessionalJobCancelledReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalJobCancelled,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":              req.JobId,
		"facilityName":       req.FacilityName,
		"jobTitle":           req.JobTitle,
		"cancellationReason": req.CancellationReason,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobCancelled,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalJobCancelledTitle,
		Content:          MsgProfessionalJobCancelledContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員工作賠付通知請求
type CreateProfessionalJobCompensationReq struct {
	UserId             uint64  `json:"userId"`             // 用戶ID
	JobId              uint64  `json:"jobId"`              // 工作ID
	ConfirmationNoteId uint64  `json:"confirmationNoteId"` // 確認單ID
	FacilityName       string  `json:"facilityName"`       // 機構名稱
	CompensationAmount float64 `json:"compensationAmount"` // 賠付金額
}

// 創建專業人員工作賠付通知
func (s *systemNotificationService) CreateProfessionalJobCompensation(db *gorm.DB, req CreateProfessionalJobCompensationReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalJobCompensation,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":              req.JobId,
		"facilityName":       req.FacilityName,
		"compensationAmount": req.CompensationAmount,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalJobCompensation,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalJobCompensationTitle,
		Content:          MsgProfessionalJobCompensationContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- Professional Job Notifications 專業人員工作通知 ----------------------------------------------------

// region ---------------------------------------------------- Professional Calendar Notifications 專業人員日程通知 ----------------------------------------------------

// 創建專業人員24小時提醒通知請求
type CreateProfessionalCalendar24HourReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
	StartTime    string `json:"startTime"`    // 開始時間
}

// 創建專業人員24小時提醒通知（單個）
func (s *systemNotificationService) CreateProfessionalCalendar24Hour(db *gorm.DB, req CreateProfessionalCalendar24HourReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalCalendar24Hour,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
		"startTime":    req.StartTime,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalCalendar24Hour,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalCalendar24HourTitle,
		Content:          MsgProfessionalCalendar24HourContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 批量創建專業人員24小時提醒通知
func (s *systemNotificationService) CreateProfessionalCalendar24HourBatch(db *gorm.DB, jobReminders []JobReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalCalendar24Hour,
		"reminderCount":    len(jobReminders),
	})

	if len(jobReminders) == 0 {
		logger.Info("No reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range jobReminders {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":     reminder.JobId,
			"jobTitle":  reminder.JobTitle,
			"startTime": reminder.BeginTime.Format(time.RFC3339),
		})
		if err != nil {
			logger.WithError(err).WithField("jobId", reminder.JobId).Error("Failed to prepare metadata")
			errorCount++
			continue
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeProfessionalCalendar24Hour,
			TargetType:       model.SystemNotificationTargetTypeProfessional,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgProfessionalCalendar24HourTitle,
			Content:          MsgProfessionalCalendar24HourContent,
			RelatedId:        reminder.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		err = s.createNotificationForUser(db, notification, reminder.UserId)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":  reminder.JobId,
				"userId": reminder.UserId,
			}).Error("Failed to create notification")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(jobReminders),
	}).Info("Batch 24 hour notification creation completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to create %d out of %d notifications", errorCount, len(jobReminders))
	}

	return nil
}

// 創建專業人員2小時提醒通知請求
type CreateProfessionalCalendar2HourReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
	StartTime    string `json:"startTime"`    // 開始時間
}

// 創建專業人員2小時提醒通知
func (s *systemNotificationService) CreateProfessionalCalendar2Hour(db *gorm.DB, req CreateProfessionalCalendar2HourReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalCalendar2Hour,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
		"startTime":    req.StartTime,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalCalendar2Hour,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalCalendar2HourTitle,
		Content:          MsgProfessionalCalendar2HourContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 批量創建專業人員2小時提醒通知
func (s *systemNotificationService) CreateProfessionalCalendar2HourBatch(db *gorm.DB, jobReminders []JobReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalCalendar2Hour,
		"reminderCount":    len(jobReminders),
	})

	if len(jobReminders) == 0 {
		logger.Info("No reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range jobReminders {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":     reminder.JobId,
			"jobTitle":  reminder.JobTitle,
			"startTime": reminder.BeginTime.Format(time.RFC3339),
		})
		if err != nil {
			logger.WithError(err).WithField("jobId", reminder.JobId).Error("Failed to prepare metadata")
			errorCount++
			continue
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeProfessionalCalendar2Hour,
			TargetType:       model.SystemNotificationTargetTypeProfessional,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgProfessionalCalendar2HourTitle,
			Content:          MsgProfessionalCalendar2HourContent,
			RelatedId:        reminder.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		err = s.createNotificationForUser(db, notification, reminder.UserId)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":  reminder.JobId,
				"userId": reminder.UserId,
			}).Error("Failed to create notification")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(jobReminders),
	}).Info("Batch 2 hour notification creation completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to create %d out of %d notifications", errorCount, len(jobReminders))
	}

	return nil
}

// endregion ---------------------------------------------------- Professional Calendar Notifications 專業人員日程通知 ----------------------------------------------------

// region ---------------------------------------------------- Professional Billing Notifications 專業人員帳單通知 ----------------------------------------------------

// 創建專業人員生成確認單通知請求
type CreateProfessionalBillingConfirmationNoteGenerateReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	JobId        uint64 `json:"jobId"`        // 工作ID
	JobTitle     string `json:"jobTitle"`     // 工作標題
	FacilityName string `json:"facilityName"` // 機構名稱
}

// 創建專業人員生成確認單通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteGenerate(db *gorm.DB, req CreateProfessionalBillingConfirmationNoteGenerateReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":        req.JobId,
		"facilityName": req.FacilityName,
		"jobTitle":     req.JobTitle,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalBillingConfirmationNoteGenerateTitle,
		Content:          MsgProfessionalBillingConfirmationNoteGenerateContent,
		RelatedId:        req.JobId,
		RelatedType:      model.SystemNotificationRelatedTypeJob,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員確認單通過通知請求
type CreateProfessionalBillingConfirmationNoteApprovedReq struct {
	UserId             uint64  `json:"userId"`             // 用戶ID
	JobId              uint64  `json:"jobId"`              // 工作ID
	ConfirmationNoteId uint64  `json:"confirmationNoteId"` // 確認單ID
	InvoiceId          uint64  `json:"invoiceId"`          // 發票ID
	FacilityId         uint64  `json:"facilityId"`         // 機構ID
	Amount             float64 `json:"amount"`             // 金額
	CreatorUserId      uint64  `json:"creatorUserId"`      // 創建用戶ID
}

// 創建專業人員確認單通過通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteApproved(db *gorm.DB, req CreateProfessionalBillingConfirmationNoteApprovedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalBillingConfirmationNoteApproved,
		"userId":           req.UserId,
		"jobId":            req.JobId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"jobId":      req.JobId,
		"facilityId": req.FacilityId,
		"invoiceId":  req.InvoiceId,
		"amount":     req.Amount,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteApproved,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalBillingConfirmationNoteApprovedTitle,
		Content:          MsgProfessionalBillingConfirmationNoteApprovedContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員確認單拒絕通知請求
type CreateProfessionalBillingConfirmationNoteRejectedReq struct {
	ConfirmationNoteId uint64 `json:"confirmationNoteId"` // 確認單ID
	RejectionReason    string `json:"rejectionReason"`    // 拒絕原因
	CreatorUserId      uint64 `json:"creatorUserId"`      // 創建用戶ID
}

// 創建專業人員確認單拒絕通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteRejected(db *gorm.DB, req CreateProfessionalBillingConfirmationNoteRejectedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType":   model.SystemNotificationTypeProfessionalBillingConfirmationNoteRejected,
		"confirmationNoteId": req.ConfirmationNoteId,
	})

	// 獲取確認單信息
	var document model.Document
	if err := db.First(&document, req.ConfirmationNoteId).Error; err != nil {
		logger.WithError(err).Error("Failed to query confirmation note record")
		return err
	}

	// 獲取機構名稱
	var facilityProfile model.FacilityProfile
	if err := db.
		Select("id, name").
		Where("facility_id = ? AND data_type = ?", document.FacilityId, model.FacilityProfileDataTypeApproved).
		First(&facilityProfile).Error; err != nil {
		logger.WithError(err).Error("Failed to query facility profile")
		return err
	}

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"facilityName":    facilityProfile.Name,
		"rejectionReason": req.RejectionReason,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteRejected,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalBillingConfirmationNoteRejectedTitle,
		Content:          MsgProfessionalBillingConfirmationNoteRejectedContent,
		RelatedId:        req.ConfirmationNoteId,
		RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, document.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 批量創建專業人員生成確認單通知
func (s *systemNotificationService) CreateProfessionalBillingConfirmationNoteGenerateBatch(db *gorm.DB, jobReminders []JobReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate,
		"reminderCount":    len(jobReminders),
	})

	if len(jobReminders) == 0 {
		logger.Info("No reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range jobReminders {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":        reminder.JobId,
			"jobTitle":     reminder.JobTitle,
			"facilityName": "", // 可以從facility表獲取，但對於確認單提醒不是必需的
		})
		if err != nil {
			logger.WithError(err).WithField("jobId", reminder.JobId).Error("Failed to prepare metadata")
			errorCount++
			continue
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeProfessionalBillingConfirmationNoteGenerate,
			TargetType:       model.SystemNotificationTargetTypeProfessional,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgProfessionalBillingConfirmationNoteGenerateTitle,
			Content:          MsgProfessionalBillingConfirmationNoteGenerateContent,
			RelatedId:        reminder.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		err = s.createNotificationForUser(db, notification, reminder.UserId)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":  reminder.JobId,
				"userId": reminder.UserId,
			}).Error("Failed to create notification")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(jobReminders),
	}).Info("Batch confirmation note generation notification creation completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to create %d out of %d notifications", errorCount, len(jobReminders))
	}

	return nil
}

// endregion ---------------------------------------------------- Professional Billing Notifications 專業人員帳單通知 ----------------------------------------------------

// region ---------------------------------------------------- Professional Profile Notifications 專業人員個人資料通知 ----------------------------------------------------

// 創建專業人員資料通過通知請求
type CreateProfessionalProfileApprovedReq struct {
	UserId        uint64 `json:"userId"`        // 用戶ID
	ProfileId     uint64 `json:"profileId"`     // 個人資料ID
	CreatorUserId uint64 `json:"creatorUserId"` // 創建用戶ID
}

// 創建專業人員資料通過通知
func (s *systemNotificationService) CreateProfessionalProfileApproved(db *gorm.DB, req CreateProfessionalProfileApprovedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalProfileApproved,
		"userId":           req.UserId,
		"profileId":        req.ProfileId,
	})

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileApproved,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalProfileApprovedTitle,
		Content:          MsgProfessionalProfileApprovedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalProfile,
		Metadata:         "{}",
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err := s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員資料駁回通知請求
type CreateProfessionalProfileRejectedReq struct {
	UserId          uint64 `json:"userId"`          // 用戶ID
	ProfileId       uint64 `json:"profileId"`       // 個人資料ID
	RejectionReason string `json:"rejectionReason"` // 駁回原因
	CreatorUserId   uint64 `json:"creatorUserId"`   // 創建用戶ID
}

// 創建專業人員資料駁回通知
func (s *systemNotificationService) CreateProfessionalProfileRejected(db *gorm.DB, req CreateProfessionalProfileRejectedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalProfileRejected,
		"userId":           req.UserId,
		"profileId":        req.ProfileId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"rejectionReason": req.RejectionReason,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileRejected,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalProfileRejectedTitle,
		Content:          MsgProfessionalProfileRejectedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員需要完善super資料通知請求
type CreateProfessionalProfileNeedSuperReq struct {
	UserId           uint64 `json:"userId"`           // 用戶ID
	SuperannuationId uint64 `json:"superannuationId"` // 養老金ID
	AbnType          string `json:"abnType"`          // ABN類型
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建專業人員需要完善super資料通知
func (s *systemNotificationService) CreateProfessionalProfileNeedSuper(db *gorm.DB, req CreateProfessionalProfileNeedSuperReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalProfileNeedSuper,
		"userId":           req.UserId,
		"superannuationId": req.SuperannuationId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"abnType": req.AbnType,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileNeedSuper,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgProfessionalProfileNeedSuperTitle,
		Content:          MsgProfessionalProfileNeedSuperContent,
		RelatedId:        req.SuperannuationId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalSuperannuation,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員文件即將到期通知請求
type CreateProfessionalProfileDocumentExpireReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	FileId       uint64 `json:"fileId"`       // 文件ID
	DocumentType string `json:"documentType"` // 文件類型
	ExpiryDate   string `json:"expiryDate"`   // 到期日期
}

// 創建專業人員文件即將到期通知
func (s *systemNotificationService) CreateProfessionalProfileDocumentExpire(db *gorm.DB, req CreateProfessionalProfileDocumentExpireReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalProfileDocumentExpire,
		"userId":           req.UserId,
		"fileId":           req.FileId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"documentType": req.DocumentType,
		"expiryDate":   req.ExpiryDate,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象，使用格式化文字
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileDocumentExpire,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalProfileDocumentExpireTitle,
		Content:          fmt.Sprintf(MsgProfessionalProfileDocumentExpireContent, req.DocumentType),
		RelatedId:        req.FileId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalFile,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建專業人員推薦人拒絕通知請求
type CreateProfessionalProfileReferenceDeclinedReq struct {
	UserId       uint64 `json:"userId"`       // 用戶ID
	ReferenceId  uint64 `json:"referenceId"`  // 推薦人ID
	RefereeName  string `json:"refereeName"`  // 推薦人姓名
	RefereeEmail string `json:"refereeEmail"` // 推薦人郵箱
}

// 創建專業人員推薦人拒絕通知
func (s *systemNotificationService) CreateProfessionalProfileReferenceDeclined(db *gorm.DB, req CreateProfessionalProfileReferenceDeclinedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeProfessionalProfileReferenceDeclined,
		"userId":           req.UserId,
		"referenceId":      req.ReferenceId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"refereeName":  req.RefereeName,
		"refereeEmail": req.RefereeEmail,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象，使用格式化文字
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeProfessionalProfileReferenceDeclined,
		TargetType:       model.SystemNotificationTargetTypeProfessional,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgProfessionalProfileReferenceDeclinedTitle,
		Content:          fmt.Sprintf(MsgProfessionalProfileReferenceDeclinedContent, req.RefereeName),
		RelatedId:        req.ReferenceId,
		RelatedType:      model.SystemNotificationRelatedTypeProfessionalReference,
		Metadata:         string(metadata),
		CreatorUserId:    0,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- Professional Profile Notifications 專業人員個人資料通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Job Notifications 機構工作通知 ----------------------------------------------------

// 創建機構工作需要確認通知請求
type CreateFacilityJobNeedConfirmReq struct {
	JobId             uint64 `json:"jobId"`             // 工作ID
	JobTitle          string `json:"jobTitle"`          // 工作標題
	StartTime         string `json:"startTime"`         // 開始時間
	ApplicationsCount int    `json:"applicationsCount"` // 申請數量
}

// 創建機構工作需要確認通知
func (s *systemNotificationService) CreateFacilityJobNeedConfirm(db *gorm.DB, req CreateFacilityJobNeedConfirmReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobNeedConfirm,
		"jobId":            req.JobId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJob(db, model.SystemNotificationTypeFacilityJobNeedConfirm, req.JobId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobTitle":          req.JobTitle,
			"startTime":         req.StartTime,
			"applicationsCount": req.ApplicationsCount,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobNeedConfirm,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityHigh,
			Title:            MsgFacilityJobNeedConfirmTitle,
			Content:          MsgFacilityJobNeedConfirmContent,
			RelatedId:        req.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		if err = s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 創建機構工作無申請通知請求
type CreateFacilityJobNoApplicationReq struct {
	JobId     uint64 `json:"jobId"`     // 工作ID
	JobTitle  string `json:"jobTitle"`  // 工作標題
	StartTime string `json:"startTime"` // 開始時間
}

// 創建機構工作無申請通知
func (s *systemNotificationService) CreateFacilityJobNoApplication(db *gorm.DB, req CreateFacilityJobNoApplicationReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobNoApplication,
		"jobId":            req.JobId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJob(db, model.SystemNotificationTypeFacilityJobNoApplication, req.JobId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobTitle":  req.JobTitle,
			"startTime": req.StartTime,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobNoApplication,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityUrgent,
			Title:            MsgFacilityJobNoApplicationTitle,
			Content:          MsgFacilityJobNoApplicationContent,
			RelatedId:        req.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 批量創建機構工作無申請通知
func (s *systemNotificationService) CreateFacilityJobNoApplicationBatch(db *gorm.DB, jobReminders []JobReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobNoApplication,
		"reminderCount":    len(jobReminders),
	})

	if len(jobReminders) == 0 {
		logger.Info("No reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0

	for _, reminder := range jobReminders {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":     reminder.JobId,
			"jobTitle":  reminder.JobTitle,
			"startTime": reminder.BeginTime.Format(time.RFC3339),
		})
		if err != nil {
			logger.WithError(err).WithField("jobId", reminder.JobId).Error("Failed to prepare metadata")
			errorCount++
			continue
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobNoApplication,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgFacilityJobNoApplicationTitle,
			Content:          MsgFacilityJobNoApplicationContent,
			RelatedId:        reminder.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		err = s.createNotificationForUser(db, notification, reminder.UserId)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":  reminder.JobId,
				"userId": reminder.UserId,
			}).Error("Failed to create notification")
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount": successCount,
		"errorCount":   errorCount,
		"totalCount":   len(jobReminders),
	}).Info("Batch facility no application notification creation completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to create %d out of %d notifications", errorCount, len(jobReminders))
	}

	return nil
}

// 創建機構工作自動取消通知請求
type CreateFacilityJobAutoCancelReq struct {
	JobId     uint64 `json:"jobId"`     // 工作ID
	JobTitle  string `json:"jobTitle"`  // 工作標題
	StartTime string `json:"startTime"` // 開始時間
}

// 創建機構工作自動取消通知
func (s *systemNotificationService) CreateFacilityJobAutoCancel(db *gorm.DB, req CreateFacilityJobAutoCancelReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobAutoCancel,
		"jobId":            req.JobId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJob(db, model.SystemNotificationTypeFacilityJobAutoCancel, req.JobId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobTitle":  req.JobTitle,
			"startTime": req.StartTime,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobAutoCancel,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityHigh,
			Title:            MsgFacilityJobAutoCancelTitle,
			Content:          MsgFacilityJobAutoCancelContent,
			RelatedId:        req.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 批量創建機構工作自動取消通知
func (s *systemNotificationService) CreateFacilityJobAutoCancelBatch(db *gorm.DB, jobReminders []JobReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobAutoCancel,
		"reminderCount":    len(jobReminders),
	})

	if len(jobReminders) == 0 {
		logger.Info("No reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0
	errorMessages := make([]string, 0)

	for _, reminder := range jobReminders {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":     reminder.JobId,
			"jobTitle":  reminder.JobTitle,
			"startTime": reminder.BeginTime.Format(time.RFC3339),
		})
		if err != nil {
			logger.WithError(err).WithField("jobId", reminder.JobId).Error("Failed to prepare metadata")
			errorMessages = append(errorMessages, fmt.Sprintf("Failed to prepare metadata for job %d: %v", reminder.JobId, err))
			errorCount++
			continue
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobAutoCancel,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgFacilityJobAutoCancelTitle,
			Content:          MsgFacilityJobAutoCancelContent,
			RelatedId:        reminder.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		err = s.createNotificationForUser(db, notification, reminder.UserId)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":  reminder.JobId,
				"userId": reminder.UserId,
			}).Error("Failed to create notification")
			errorMessages = append(errorMessages, fmt.Sprintf("Failed to create notification for job %d: %v", reminder.JobId, err))
			errorCount++
		} else {
			successCount++
		}
	}

	logger.WithFields(log.Fields{
		"successCount":  successCount,
		"errorCount":    errorCount,
		"totalCount":    len(jobReminders),
		"errorMessages": errorMessages,
	}).Info("Batch facility auto cancel notification creation completed")

	if errorCount > 0 {
		return fmt.Errorf("failed to create %d out of %d notifications: %v", errorCount, len(jobReminders), errorMessages)
	}

	return nil
}

// 創建機構工作接受通知請求
type CreateFacilityJobAcceptedReq struct {
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建機構工作接受通知
func (s *systemNotificationService) CreateFacilityJobAccepted(db *gorm.DB, req CreateFacilityJobAcceptedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobAccepted,
		"jobId":            req.JobId,
		"jobApplicationId": req.JobApplicationId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJobApplicationId(db, model.SystemNotificationTypeFacilityJobAccepted, req.JobApplicationId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		logger := logger.WithField("facilityUserId", facilityUserId)
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobApplicationId": req.JobApplicationId,
			"jobId":            req.JobId,
			"professionalName": req.ProfessionalName,
			"jobTitle":         req.JobTitle,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobAccepted,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgFacilityJobAcceptedTitle,
			Content:          MsgFacilityJobAcceptedContent,
			RelatedId:        req.JobApplicationId,
			RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
			Metadata:         string(metadata),
			CreatorUserId:    req.CreatorUserId,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 創建機構工作拒絕通知請求
type CreateFacilityJobDeclineReq struct {
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobApplicationId uint64 `json:"jobApplicationId"` // 工作申請ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
}

// 創建機構工作拒絕通知
func (s *systemNotificationService) CreateFacilityJobDecline(db *gorm.DB, req CreateFacilityJobDeclineReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobDecline,
		"jobId":            req.JobId,
		"jobApplicationId": req.JobApplicationId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJobApplicationId(db, model.SystemNotificationTypeFacilityJobDecline, req.JobApplicationId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobApplicationId": req.JobApplicationId,
			"jobId":            req.JobId,
			"professionalName": req.ProfessionalName,
			"jobTitle":         req.JobTitle,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象，使用格式化文字
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobDecline,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgFacilityJobDeclineTitle,
			Content:          fmt.Sprintf(MsgFacilityJobDeclineContent, req.ProfessionalName),
			RelatedId:        req.JobApplicationId,
			RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
			Metadata:         string(metadata),
			CreatorUserId:    req.CreatorUserId,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 創建機構工作邀請過期通知請求
type CreateFacilityJobInvitationExpiredReq struct {
	JobApplicationIds []uint64 `json:"jobApplicationIds"` // 工作申請ID
}

// 創建機構工作邀請過期通知
func (s *systemNotificationService) CreateFacilityJobInvitationExpired(db *gorm.DB, req CreateFacilityJobInvitationExpiredReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobInvitationExpired,
		"applicationCount": len(req.JobApplicationIds),
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	for _, jobApplicationId := range req.JobApplicationIds {
		facilityUserIds, err := s.GetFacilityUserIdsByJobApplicationId(db, model.SystemNotificationTypeFacilityJobInvitationExpired, jobApplicationId)
		if err != nil {
			logger.WithError(err).Error("Failed to get facility user IDs")
			return err
		}
		for _, facilityUserId := range facilityUserIds {
			// 準備元數據
			var jobApplication model.JobApplication
			if err = db.First(&jobApplication, jobApplicationId).Error; err != nil {
				logger.WithError(err).Error("Failed to query job application")
				return err
			}
			metadata, err := json.Marshal(map[string]interface{}{
				"jobId":            jobApplication.JobId,
				"jobApplicationId": jobApplicationId,
			})
			if err != nil {
				logger.WithError(err).Error("Failed to prepare metadata")
				return err
			}

			// 創建通知對象
			notification := model.SystemNotification{
				NotificationType: model.SystemNotificationTypeFacilityJobInvitationExpired,
				TargetType:       model.SystemNotificationTargetTypeFacility,
				Priority:         model.SystemNotificationPriorityNormal,
				Title:            MsgFacilityJobInvitationExpiredTitle,
				Content:          MsgFacilityJobInvitationExpiredContent,
				RelatedId:        jobApplicationId,
				RelatedType:      model.SystemNotificationRelatedTypeJobApplication,
				Metadata:         string(metadata),
				CreatorUserId:    0,
				CreateTime:       time.Now().UTC(),
			}
			if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
				logger.WithError(err).Error("Failed to create notification")
				return err
			}
		}
	}
	return nil
}

// 創建機構工作被取消通知請求
type CreateFacilityJobCancelledReq struct {
	JobId              uint64 `json:"jobId"`              // 工作ID
	JobTitle           string `json:"jobTitle"`           // 工作標題
	ProfessionalName   string `json:"professionalName"`   // 專業人員姓名
	CancellationReason string `json:"cancellationReason"` // 取消原因
	CreatorUserId      uint64 `json:"creatorUserId"`      // 創建用戶ID
}

// 創建機構工作被取消通知
func (s *systemNotificationService) CreateFacilityJobCancelled(db *gorm.DB, req CreateFacilityJobCancelledReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityJobCancelled,
		"jobId":            req.JobId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJob(db, model.SystemNotificationTypeFacilityJobCancelled, req.JobId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":              req.JobId,
			"professionalName":   req.ProfessionalName,
			"jobTitle":           req.JobTitle,
			"cancellationReason": req.CancellationReason,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityJobCancelled,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityHigh,
			Title:            MsgFacilityJobCancelledTitle,
			Content:          MsgFacilityJobCancelledContent,
			RelatedId:        req.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    req.CreatorUserId,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- Facility Job Notifications 機構工作通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Calendar Notifications 機構日程通知 ----------------------------------------------------

// 創建機構24小時提醒通知請求
type CreateFacilityCalendar24HourReq struct {
	JobId            uint64 `json:"jobId"`            // 工作ID
	JobTitle         string `json:"jobTitle"`         // 工作標題
	ProfessionalName string `json:"professionalName"` // 專業人員姓名
	StartTime        string `json:"startTime"`        // 開始時間
}

// 創建機構24小時提醒通知
func (s *systemNotificationService) CreateFacilityCalendar24Hour(db *gorm.DB, req CreateFacilityCalendar24HourReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityCalendar24Hour,
		"jobId":            req.JobId,
	})

	// 根據Job和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJob(db, model.SystemNotificationTypeFacilityCalendar24Hour, req.JobId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":            req.JobId,
			"professionalName": req.ProfessionalName,
			"jobTitle":         req.JobTitle,
			"startTime":        req.StartTime,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityCalendar24Hour,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgFacilityCalendar24HourTitle,
			Content:          MsgFacilityCalendar24HourContent,
			RelatedId:        req.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 批量創建機構24小時提醒通知
func (s *systemNotificationService) CreateFacilityCalendar24HourBatch(db *gorm.DB, jobReminders []JobReminderInfo) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityCalendar24Hour,
		"reminderCount":    len(jobReminders),
	})

	if len(jobReminders) == 0 {
		logger.Info("No reminders to process")
		return nil
	}

	successCount := 0
	errorCount := 0
	var errorMessages []string

	for _, reminder := range jobReminders {
		// 準備元數據
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":     reminder.JobId,
			"jobTitle":  reminder.JobTitle,
			"startTime": reminder.BeginTime.Format(time.RFC3339),
		})
		if err != nil {
			logger.WithError(err).WithField("jobId", reminder.JobId).Error("Failed to prepare metadata")
			errorCount++
			errorMessages = append(errorMessages, err.Error())
			continue
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityCalendar24Hour,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityNormal,
			Title:            MsgFacilityCalendar24HourTitle,
			Content:          MsgFacilityCalendar24HourContent,
			RelatedId:        reminder.JobId,
			RelatedType:      model.SystemNotificationRelatedTypeJob,
			Metadata:         string(metadata),
			CreatorUserId:    0,
			CreateTime:       time.Now().UTC(),
		}

		err = s.createNotificationForUser(db, notification, reminder.UserId)
		if err != nil {
			logger.WithError(err).WithFields(log.Fields{
				"jobId":  reminder.JobId,
				"userId": reminder.UserId,
			}).Error("Failed to create notification")
			errorMessages = append(errorMessages, err.Error())
			errorCount++
		} else {
			successCount++
		}
	}

	if errorCount > 0 {
		logger.WithFields(log.Fields{
			"successCount":  successCount,
			"errorCount":    errorCount,
			"totalCount":    len(jobReminders),
			"errorMessages": errorMessages,
		}).Error("Batch facility 24 hour notification creation failed")
		return fmt.Errorf("failed to create %d out of %d notifications: %v", errorCount, len(jobReminders), errorMessages)
	}

	return nil
}

// endregion ---------------------------------------------------- Facility Calendar Notifications 機構日程通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Billing Notifications 機構帳單通知 ----------------------------------------------------

// 創建機構收到確認單通知請求
type CreateFacilityBillingConfirmationNoteReceivedReq struct {
	JobApplicationId   uint64          `json:"jobApplicationId"`   // 工作申請ID
	ConfirmationNoteId uint64          `json:"confirmationNoteId"` // 確認單ID
	GrandTotal         decimal.Decimal `json:"grandTotal"`         // 總金額
}

// 創建機構收到確認單通知
func (s *systemNotificationService) CreateFacilityBillingConfirmationNoteReceived(db *gorm.DB, req CreateFacilityBillingConfirmationNoteReceivedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType":   model.SystemNotificationTypeFacilityBillingConfirmationNoteReceived,
		"jobApplicationId":   req.JobApplicationId,
		"confirmationNoteId": req.ConfirmationNoteId,
	})

	// 根據 JobApplicationId 獲取相關信息
	var jobApplication model.JobApplication
	if err := db.Select("job_id, professional_id").Where("id = ?", req.JobApplicationId).First(&jobApplication).Error; err != nil {
		logger.WithError(err).Error("Failed to query job application record")
		return err
	}

	// 獲取專業人員信息
	var professional model.Professional
	if err := db.Select("first_name, last_name, user_id").Where("id = ?", jobApplication.ProfessionalId).First(&professional).Error; err != nil {
		logger.WithError(err).Error("Failed to query professional record")
		return err
	}

	// 根據JobApplication和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJobApplicationId(db, model.SystemNotificationTypeFacilityBillingConfirmationNoteReceived, req.JobApplicationId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		grandTotalFloat, _ := req.GrandTotal.Float64()
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":            jobApplication.JobId,
			"professionalName": professional.FirstName + " " + professional.LastName,
			"amount":           grandTotalFloat,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityBillingConfirmationNoteReceived,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityHigh,
			Title:            MsgFacilityBillingConfirmationNoteReceivedTitle,
			Content:          MsgFacilityBillingConfirmationNoteReceivedContent,
			RelatedId:        req.ConfirmationNoteId,
			RelatedType:      model.SystemNotificationRelatedTypeConfirmationNote,
			Metadata:         string(metadata),
			CreatorUserId:    professional.UserId,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// 創建機構收到發票通知請求
type CreateFacilityBillingInvoiceReceivedReq struct {
	JobApplicationId uint64          `json:"jobApplicationId"` // 工作申請ID
	InvoiceId        uint64          `json:"invoiceId"`        // 發票ID
	GrandTotal       decimal.Decimal `json:"grandTotal"`       // 總金額
	DueDate          string          `json:"dueDate"`          // 到期日期
}

// 創建機構收到發票通知
func (s *systemNotificationService) CreateFacilityBillingInvoiceReceived(db *gorm.DB, req CreateFacilityBillingInvoiceReceivedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityBillingInvoiceReceived,
		"jobApplicationId": req.JobApplicationId,
		"invoiceId":        req.InvoiceId,
	})

	// 根據 JobApplicationId 獲取相關信息
	var jobApplication model.JobApplication
	if err := db.Select("job_id, professional_id").Where("id = ?", req.JobApplicationId).First(&jobApplication).Error; err != nil {
		logger.WithError(err).Error("Failed to query job application record")
		return err
	}

	// 獲取專業人員信息
	var professional model.Professional
	if err := db.Select("first_name, last_name, user_id").Where("id = ?", jobApplication.ProfessionalId).First(&professional).Error; err != nil {
		logger.WithError(err).Error("Failed to query professional record")
		return err
	}

	// 根據JobApplication和通知類型獲取相應的機構用戶ID
	facilityUserIds, err := s.GetFacilityUserIdsByJobApplicationId(db, model.SystemNotificationTypeFacilityBillingInvoiceReceived, req.JobApplicationId)
	if err != nil {
		logger.WithError(err).Error("Failed to get facility user IDs")
		return err
	}

	for _, facilityUserId := range facilityUserIds {
		// 準備元數據
		grandTotalFloat, _ := req.GrandTotal.Float64()
		metadata, err := json.Marshal(map[string]interface{}{
			"jobId":            jobApplication.JobId,
			"professionalName": professional.FirstName + " " + professional.LastName,
			"amount":           grandTotalFloat,
			"dueDate":          req.DueDate,
		})
		if err != nil {
			logger.WithError(err).Error("Failed to prepare metadata")
			return err
		}

		// 創建通知對象
		notification := model.SystemNotification{
			NotificationType: model.SystemNotificationTypeFacilityBillingInvoiceReceived,
			TargetType:       model.SystemNotificationTargetTypeFacility,
			Priority:         model.SystemNotificationPriorityHigh,
			Title:            MsgFacilityBillingInvoiceReceivedTitle,
			Content:          MsgFacilityBillingInvoiceReceivedContent,
			RelatedId:        req.InvoiceId,
			RelatedType:      model.SystemNotificationRelatedTypeInvoice,
			Metadata:         string(metadata),
			CreatorUserId:    professional.UserId,
			CreateTime:       time.Now().UTC(),
		}

		if err := s.createNotificationForUser(db, notification, facilityUserId); err != nil {
			logger.WithError(err).Error("Failed to create notification")
			return err
		}
	}

	return nil
}

// endregion ---------------------------------------------------- Facility Billing Notifications 機構帳單通知 ----------------------------------------------------

// region ---------------------------------------------------- Facility Information Notifications 機構信息通知 ----------------------------------------------------

// 創建機構信息通過通知請求
type CreateFacilityInformationApprovedReq struct {
	UserId        uint64 `json:"userId"`        // 用戶ID
	ProfileId     uint64 `json:"profileId"`     // 個人資料ID
	CreatorUserId uint64 `json:"creatorUserId"` // 創建用戶ID
}

// 創建機構信息通過通知
func (s *systemNotificationService) CreateFacilityInformationApproved(db *gorm.DB, req CreateFacilityInformationApprovedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityInformationApproved,
		"userId":           req.UserId,
		"profileId":        req.ProfileId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityInformationApproved,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            MsgFacilityInformationApprovedTitle,
		Content:          MsgFacilityInformationApprovedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeFacilityProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// 創建機構信息駁回通知請求
type CreateFacilityInformationRejectedReq struct {
	ProfileId       uint64 `json:"profileId"`       // 個人資料ID
	RejectionReason string `json:"rejectionReason"` // 駁回原因
	CreatorUserId   uint64 `json:"creatorUserId"`   // 創建用戶ID
}

// 創建機構信息駁回通知
func (s *systemNotificationService) CreateFacilityInformationRejected(db *gorm.DB, req CreateFacilityInformationRejectedReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeFacilityInformationRejected,
		"profileId":        req.ProfileId,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"rejectionReason": req.RejectionReason,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeFacilityInformationRejected,
		TargetType:       model.SystemNotificationTargetTypeFacility,
		Priority:         model.SystemNotificationPriorityHigh,
		Title:            MsgFacilityInformationRejectedTitle,
		Content:          MsgFacilityInformationRejectedContent,
		RelatedId:        req.ProfileId,
		RelatedType:      model.SystemNotificationRelatedTypeFacilityProfile,
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createNotificationForUser(db, notification, req.UserId)
	if err != nil {
		logger.WithError(err).Error("Failed to create notification")
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- Facility Information Notifications 機構信息通知 ----------------------------------------------------

// region ---------------------------------------------------- System Notifications 系統通知 ----------------------------------------------------

// 創建系統公告通知請求
type CreateSystemAnnouncementReq struct {
	Title         string   `json:"title"`         // 標題
	Content       string   `json:"content"`       // 內容
	FeatureList   []string `json:"featureList"`   // 功能列表
	CreatorUserId uint64   `json:"creatorUserId"` // 創建用戶ID
}

// 創建系統公告通知
func (s *systemNotificationService) CreateSystemAnnouncement(db *gorm.DB, req CreateSystemAnnouncementReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeSystemAnnouncement,
		"title":            req.Title,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"featureList": req.FeatureList,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeSystemAnnouncement,
		TargetType:       model.SystemNotificationTargetTypeAll,
		Priority:         model.SystemNotificationPriorityNormal,
		Title:            req.Title,
		Content:          req.Content,
		RelatedId:        0,
		RelatedType:      "",
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createSystemNotification(db, notification)
	if err != nil {
		logger.WithError(err).Error("Failed to create system notification")
		return err
	}

	return nil
}

// 創建維護通知請求
type CreateMaintenanceNoticeReq struct {
	Title         string `json:"title"`         // 標題
	Content       string `json:"content"`       // 內容
	StartTime     string `json:"startTime"`     // 開始時間
	EndTime       string `json:"endTime"`       // 結束時間
	CreatorUserId uint64 `json:"creatorUserId"` // 創建用戶ID
}

// 創建維護通知
func (s *systemNotificationService) CreateMaintenanceNotice(db *gorm.DB, req CreateMaintenanceNoticeReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": model.SystemNotificationTypeMaintenanceNotice,
		"title":            req.Title,
	})

	// 準備元數據
	metadata, err := json.Marshal(map[string]interface{}{
		"startTime": req.StartTime,
		"endTime":   req.EndTime,
	})
	if err != nil {
		logger.WithError(err).Error("Failed to prepare metadata")
		return err
	}

	// 創建通知對象
	notification := model.SystemNotification{
		NotificationType: model.SystemNotificationTypeMaintenanceNotice,
		TargetType:       model.SystemNotificationTargetTypeAll,
		Priority:         model.SystemNotificationPriorityUrgent,
		Title:            req.Title,
		Content:          req.Content,
		RelatedId:        0,
		RelatedType:      "",
		Metadata:         string(metadata),
		CreatorUserId:    req.CreatorUserId,
		CreateTime:       time.Now().UTC(),
	}

	err = s.createSystemNotification(db, notification)
	if err != nil {
		logger.WithError(err).Error("Failed to create system notification")
		return err
	}

	return nil
}

// endregion ---------------------------------------------------- System Notifications 系統通知 ----------------------------------------------------

// region ---------------------------------------------------- Notification Query APIs 通知查詢API ----------------------------------------------------

// 獲取用戶通知列表請求
type GetUserNotificationListReq struct {
	UserId     uint64 `form:"-" json:"-"`                               // 用戶ID，在控制器中設置
	ReadStatus string `form:"readStatus" binding:"omitempty,oneof=Y N"` // 已讀狀態: Y=已讀, N=未讀
}

// 用戶通知項目
type UserNotificationItem struct {
	Id               uint64 `json:"id"`               // 通知ID
	NotificationType string `json:"notificationType"` // 通知類型
	TargetType       string `json:"targetType"`       // 目標類型
	Priority         string `json:"priority"`         // 優先級
	Title            string `json:"title"`            // 通知標題
	Content          string `json:"content"`          // 通知內容
	RelatedId        uint64 `json:"relatedId"`        // 關聯業務ID
	RelatedType      string `json:"relatedType"`      // 關聯業務類型
	Metadata         string `json:"metadata"`         // 額外元數據JSON
	CreatorUserId    uint64 `json:"creatorUserId"`    // 創建用戶ID
	CreateTime       string `json:"createTime"`       // 創建時間
	ReadStatus       string `json:"readStatus"`       // 是否已讀 Y/N
	ReadTime         string `json:"readTime"`         // 已讀時間
}

// 獲取用戶通知列表
func (s *systemNotificationService) GetUserNotificationList(db *gorm.DB, req GetUserNotificationListReq, pageSet *xresp.PageSet) ([]UserNotificationItem, error) {
	// 構建基礎查詢
	builder := db.Table("system_notification_user AS snu").
		Joins("JOIN system_notification AS sn ON sn.id = snu.system_notification_id").
		Where("snu.user_id = ?", req.UserId).
		Where("snu.deleted = ?", model.SystemNotificationUserDeletedN)

	// 根據已讀狀態篩選
	if req.ReadStatus != "" {
		builder = builder.Where("snu.read_status = ?", req.ReadStatus)
	}

	// 查詢通知列表
	notifications := make([]UserNotificationItem, 0)
	err := builder.
		Select([]string{
			"sn.id",
			"sn.notification_type",
			"sn.target_type",
			"sn.priority",
			"sn.title",
			"sn.content",
			"sn.related_id",
			"sn.related_type",
			"sn.metadata",
			"sn.creator_user_id",
			"sn.create_time",
			"snu.read_status",
			"COALESCE(snu.read_time, '') as read_time",
		}).
		Scopes(xresp.Paginate(pageSet)).
		Order("sn.create_time DESC").
		Find(&notifications).Error

	if err != nil {
		return nil, err
	}

	return notifications, nil
}

// 標記通知讀取狀態請求
type MarkNotificationAsReadReq struct {
	UserId                uint64   `json:"-"`                                        // 用戶ID，在控制器中設置
	SystemNotificationIds []uint64 `json:"systemNotificationIds" binding:"required"` // 通知ID列表
	ReadStatus            string   `json:"readStatus" binding:"required,oneof=Y N"`  // 讀取狀態: Y=已讀, N=未讀
}

// 標記通知讀取狀態
func (s *systemNotificationService) MarkNotificationAsRead(db *gorm.DB, req MarkNotificationAsReadReq) error {
	if len(req.SystemNotificationIds) == 0 {
		return nil
	}

	// 根據請求的狀態更新通知
	updateData := map[string]interface{}{
		"read_status": req.ReadStatus,
	}

	// 如果是標記為已讀，設置讀取時間；如果是標記為未讀，清空讀取時間
	if req.ReadStatus == model.SystemNotificationUserReadY {
		updateData["read_time"] = time.Now().UTC()
	} else {
		updateData["read_time"] = nil
	}

	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("system_notification_id IN ?", req.SystemNotificationIds).
		Where("deleted = ?", model.SystemNotificationUserDeletedN).
		Updates(updateData).Error

	return err
}

// 標記所有通知為已讀請求
type MarkAllNotificationsAsReadReq struct {
	UserId uint64 `json:"-"` // 用戶ID，在控制器中設置
}

// 標記所有通知為已讀
func (s *systemNotificationService) MarkAllNotificationsAsRead(db *gorm.DB, req MarkAllNotificationsAsReadReq) error {
	// 更新所有未讀通知為已讀狀態
	now := time.Now().UTC()
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("read_status = ?", model.SystemNotificationUserReadN).
		Where("deleted = ?", model.SystemNotificationUserDeletedN).
		Updates(map[string]interface{}{
			"read_status": model.SystemNotificationUserReadY,
			"read_time":   now,
		}).Error

	return err
}

// 刪除通知請求
type DeleteNotificationReq struct {
	UserId                uint64   `json:"-"`                                        // 用戶ID，在控制器中設置
	SystemNotificationIds []uint64 `json:"systemNotificationIds" binding:"required"` // 通知ID列表
}

// 刪除通知（軟刪除）
func (s *systemNotificationService) DeleteNotification(db *gorm.DB, req DeleteNotificationReq) error {
	if len(req.SystemNotificationIds) == 0 {
		return nil
	}

	// 軟刪除通知
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("system_notification_id IN ?", req.SystemNotificationIds).
		Update("deleted", model.SystemNotificationUserDeletedY).Error

	return err
}

// 獲取未讀數量請求
type GetUnreadCountReq struct {
	UserId uint64 `json:"-"` // 用戶ID，在控制器中設置
}

// 獲取未讀數量響應
type GetUnreadCountResp struct {
	UnreadCount int64 `json:"unreadCount"` // 未讀數量
}

// 獲取未讀通知數量
func (s *systemNotificationService) GetUnreadCount(db *gorm.DB, req GetUnreadCountReq) (*GetUnreadCountResp, error) {
	var unreadCount int64
	err := db.Table("system_notification_user").
		Where("user_id = ?", req.UserId).
		Where("read_status = ?", model.SystemNotificationUserReadN).
		Where("deleted = ?", model.SystemNotificationUserDeletedN).
		Count(&unreadCount).Error

	if err != nil {
		return nil, err
	}

	return &GetUnreadCountResp{
		UnreadCount: unreadCount,
	}, nil
}

// endregion ---------------------------------------------------- Notification Query APIs 通知查詢API ----------------------------------------------------

// region ---------------------------------------------------- Helper Methods 輔助方法 ----------------------------------------------------

// createNotificationForUser 為單個用戶創建通知
func (s *systemNotificationService) createNotificationForUser(db *gorm.DB, notification model.SystemNotification, userId uint64) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": notification.NotificationType,
		"userId":           userId,
		"relatedId":        notification.RelatedId,
	})

	// 創建系統通知記錄
	if err := db.Create(&notification).Error; err != nil {
		logger.WithError(err).Error("Failed to create system notification record")
		return err
	}

	// 創建用戶通知關聯記錄
	userNotification := model.SystemNotificationUser{
		UserId:               userId,
		SystemNotificationId: notification.Id,
		ReadStatus:           model.SystemNotificationUserReadN,
		ReadTime:             nil,
		Deleted:              model.SystemNotificationUserDeletedN,
	}

	err := db.Create(&userNotification).Error
	if err != nil {
		logger.WithError(err).Error("Failed to create user notification record")
		return err
	}

	return nil
}

// createSystemNotification 為所有用戶創建系統通知
func (s *systemNotificationService) createSystemNotification(db *gorm.DB, notification model.SystemNotification) error {
	return db.Create(&notification).Error
}

// createNotificationForUsers 批量為多個用戶創建通知
func (s *systemNotificationService) createNotificationForUsers(db *gorm.DB, notification model.SystemNotification, userIds []uint64) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": notification.NotificationType,
		"userCount":        len(userIds),
		"relatedId":        notification.RelatedId,
	})

	// 創建系統通知記錄
	if err := db.Create(&notification).Error; err != nil {
		logger.WithError(err).Error("Failed to create system notification record")
		return err
	}

	// 批量創建用戶通知關聯記錄
	var userNotifications []model.SystemNotificationUser
	for _, userId := range userIds {
		userNotifications = append(userNotifications, model.SystemNotificationUser{
			UserId:               userId,
			SystemNotificationId: notification.Id,
			ReadStatus:           model.SystemNotificationUserReadN,
			ReadTime:             nil,
			Deleted:              model.SystemNotificationUserDeletedN,
		})
	}

	if len(userNotifications) > 0 {
		err := db.CreateInBatches(userNotifications, 100).Error
		if err != nil {
			logger.WithError(err).Error("Failed to create user notification records in batch")
			return err
		}
	}

	return nil
}

// 根據用戶類型發送通知請求
type SendNotificationByUserTypeReq struct {
	Notification model.SystemNotification `json:"notification"` // 通知對象
	UserType     string                   `json:"userType"`     // 用戶類型: PROFESSIONAL, FACILITY
}

// 根據用戶類型發送通知
func (s *systemNotificationService) SendNotificationByUserType(db *gorm.DB, req SendNotificationByUserTypeReq) error {
	logger := log.WithFields(log.Fields{
		"NotificationType": req.Notification.NotificationType,
		"userType":         req.UserType,
	})

	// 創建系統通知記錄
	if err := db.Create(&req.Notification).Error; err != nil {
		logger.WithError(err).Error("Failed to create system notification record")
		return err
	}

	// 根據用戶類型獲取用戶列表
	var userIds []uint64
	var users []xmodel.User

	switch req.UserType {
	case model.SystemNotificationTargetTypeProfessional:
		err := db.Where("user_type = ?", model.UserUserTypeProfessional).Find(&users).Error
		if err != nil {
			logger.WithError(err).Error("Failed to query professional users")
			return err
		}
	case model.SystemNotificationTargetTypeFacility:
		err := db.Where("user_type = ?", model.UserUserTypeFacilityUser).Find(&users).Error
		if err != nil {
			logger.WithError(err).Error("Failed to query facility users")
			return err
		}
	}

	for _, user := range users {
		userIds = append(userIds, user.Id)
	}

	// 批量創建用戶通知關聯記錄
	var userNotifications []model.SystemNotificationUser
	for _, userId := range userIds {
		userNotifications = append(userNotifications, model.SystemNotificationUser{
			UserId:               userId,
			SystemNotificationId: req.Notification.Id,
			ReadStatus:           model.SystemNotificationUserReadN,
			ReadTime:             nil,
			Deleted:              model.SystemNotificationUserDeletedN,
		})
	}

	if len(userNotifications) > 0 {
		err := db.CreateInBatches(userNotifications, 100).Error
		if err != nil {
			logger.WithError(err).Error("Failed to create user notification records in batch")
			return err
		}
	}

	return nil
}

// 根據工作申請ID獲取相關的機構用戶ID
func (s *systemNotificationService) GetFacilityUserIdsByJobApplicationId(db *gorm.DB, notificationType string, jobApplicationId uint64) ([]uint64, error) {
	logger := log.WithFields(log.Fields{
		"NotificationType": notificationType,
		"jobApplicationId": jobApplicationId,
	})

	var permissionCode string
	var ok bool
	if permissionCode, ok = FacilityNotificationUserPermissionCodeMap[notificationType]; !ok {
		logger.WithField("notificationType", notificationType).Error("Permission code not found for notification type")
		return nil, errors.New("permission code is empty")
	}

	// 1. 根據 jobApplicationId 找到對應的 job 和 facilityId
	var jobApplication model.JobApplication
	if err := db.Select("facility_id, job_id").Where("id = ?", jobApplicationId).First(&jobApplication).Error; err != nil {
		logger.WithError(err).Error("Failed to query job application record")
		return nil, err
	}

	// 2. 查詢該機構下具有指定權限且屬於相關部門的用戶ID
	var userIds []uint64
	err := db.Table("facility_user fu").
		Select("DISTINCT fu.user_id").
		Joins("JOIN user_role ur ON ur.user_id = fu.user_id").
		Joins("JOIN role r ON r.id = ur.role_id").
		Joins("JOIN role_action ra ON ra.role_id = r.id").
		Joins("JOIN action a ON a.id = ra.action_id").
		Joins("JOIN facility_user_department fud ON fud.facility_user_id = fu.id").
		Joins("JOIN job_department jd ON jd.department_id = fud.department_id AND jd.job_id = ?", jobApplication.JobId).
		Where("fu.facility_id = ? AND a.code = ?", jobApplication.FacilityId, permissionCode).
		Pluck("fu.user_id", &userIds).Error

	if err != nil {
		logger.WithError(err).Error("Failed to query facility user permissions with department")
		return nil, err
	}

	return userIds, nil
}

// 根據工作ID獲取相關的機構用戶ID（考慮部門權限）
func (s *systemNotificationService) GetFacilityUserIdsByJob(db *gorm.DB, notificationType string, jobId uint64) ([]uint64, error) {
	logger := log.WithFields(log.Fields{
		"NotificationType": notificationType,
		"jobId":            jobId,
	})

	var permissionCode string
	var ok bool
	if permissionCode, ok = FacilityNotificationUserPermissionCodeMap[notificationType]; !ok {
		logger.WithField("notificationType", notificationType).Error("Permission code not found for notification type")
		return nil, errors.New("permission code is empty")
	}

	// 1. 根據 jobId 找到對應的 facilityId
	var job model.Job
	if err := db.Select("facility_id").Where("id = ?", jobId).First(&job).Error; err != nil {
		logger.WithError(err).Error("Failed to query job record")
		return nil, err
	}

	// 2. 查詢該機構下具有指定權限且屬於相關部門的用戶ID
	var userIds []uint64
	err := db.Table("facility_user fu").
		Select("DISTINCT fu.user_id").
		Joins("JOIN user_role ur ON ur.user_id = fu.user_id").
		Joins("JOIN role r ON r.id = ur.role_id").
		Joins("JOIN role_action ra ON ra.role_id = r.id").
		Joins("JOIN action a ON a.id = ra.action_id").
		Joins("JOIN facility_user_department fud ON fud.facility_user_id = fu.id").
		Joins("JOIN job_department jd ON jd.department_id = fud.department_id AND jd.job_id = ?", jobId).
		Where("fu.facility_id = ? AND a.code = ?", job.FacilityId, permissionCode).
		Pluck("fu.user_id", &userIds).Error

	if err != nil {
		logger.WithError(err).Error("Failed to query facility user permissions with department")
		return nil, err
	}

	return userIds, nil
}

// endregion ---------------------------------------------------- Helper Methods 輔助方法 ----------------------------------------------------
