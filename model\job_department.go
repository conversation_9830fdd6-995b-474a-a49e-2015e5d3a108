package model

import (
	"github.com/Norray/xrocket/xmodel"
)

// 工作部門
type JobDepartment struct {
	Id           uint64 `json:"id" gorm:"primary_key"`                             // 主鍵
	FacilityId   uint64 `json:"facilityId" gorm:"index:facility_idx;not null"`     // 機構Id
	DepartmentId uint64 `json:"departmentId" gorm:"index:department_idx;not null"` // 部門Id
	JobId        uint64 `json:"jobId" gorm:"index:job_idx;not null"`               // 工作Id
	xmodel.Model
}

func (JobDepartment) TableName() string {
	return "job_department"
}

func (JobDepartment) SwaggerDescription() string {
	return "工作部門"
}
