package job

import (
	"context"
	"fmt"
	"time"

	"github.com/Norray/medic-crew/model"
	"github.com/Norray/medic-crew/services"
	"github.com/Norray/xrocket/xgorm"
	uuid "github.com/satori/go.uuid"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

const (
	CronJobAutoCancel                    = "cron_job_auto_cancel" // 工作自動取消
	JobAutoCancelMaxProcessRecordsPerRun = 100                    // 每次處理的最大記錄數
	JobAutoCancelLockTimeoutSeconds      = 50                     // 鎖定超時時間（秒）
)

// 工作自動取消定時任務 - 每分鐘執行
func jobAutoCancel1Hour() {
	traceId := uuid.NewV4().String()
	ctx := context.Background()
	ctx = context.WithValue(ctx, "traceId", traceId)

	logger := log.WithField("traceId", traceId).WithField("task", CronJobAutoCancel)

	db := xgorm.DB.WithContext(ctx)
	run, _, err := services.CronSettingService.CheckCronRunning(db, CronJobAutoCancel)
	if err != nil {
		logger.Errorf("[CRON] fail to check job auto cancel task: %v", err)
		return
	}
	if !run {
		logger.Warnf("[CRON] <%s> cron job not run ", CronJobAutoCancel)
		return
	}

	nowTime := time.Now().UTC().Truncate(time.Second)

	// 檢查工作開始前1小時無人申請自動取消 - Facility
	checkJobAutoCancel1Hour(db, nowTime, logger)

	logger.Info("job auto cancel check completed")
}

// 檢查工作開始前1小時無人申請自動取消
func checkJobAutoCancel1Hour(db *gorm.DB, nowTime time.Time, logger *log.Entry) {
	// 距離工作開始1小時前還未有人申請工作，工作自動取消 - 通知Facility

	var jobReminders []services.JobReminderInfo
	builder := db.Table("job AS j").
		Select([]string{
			"j.id AS job_id",
			"0 AS job_application_id", // 自動取消提醒不需要具體的申請ID
			"j.facility_id",
			"j.position_profession AS job_title",
			"j.begin_time",
			"fu.user_id",
			fmt.Sprintf("'%s' AS reminder_type", services.ReminderTypeAutoCancel1Hour),
		}).
		Joins("JOIN facility_user AS fu ON fu.facility_id = j.facility_id AND fu.primary_user = 'Y'").
		Joins("LEFT JOIN job_application AS ja ON ja.job_id = j.id").
		Where("j.status = ?", model.JobStatusPublish).
		Where("j.begin_time > ?", nowTime).
		Where("j.begin_time <= ?", nowTime.Add(1*time.Hour)).
		Where("ja.id IS NULL"). // 無申請記錄
		Group("j.id, j.facility_id, fu.user_id, j.begin_time").
		Limit(JobAutoCancelMaxProcessRecordsPerRun)

	if err := builder.Scan(&jobReminders).Error; err != nil {
		logger.Errorf("[CRON] fail to get auto cancel 1 hour reminders: %v", err)
		return
	}

	// 設置事務超時
	ctx, cancel := context.WithTimeout(context.Background(), JobAutoCancelLockTimeoutSeconds*time.Second)
	defer cancel()

	var cancelledJobs []services.JobReminderInfo

	// 在單一事務中處理所有工作取消和通知發送
	err := db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, reminder := range jobReminders {
			// 自動取消工作
			result := tx.Model(&model.Job{}).
				Where("id = ?", reminder.JobId).
				Where("status = ?", model.JobStatusPublish).
				Updates(map[string]interface{}{
					"status":        model.JobStatusCancel,
					"cancel_reason": "No applications received within 1 hour of start time",
					"update_time":   nowTime,
				})

			if result.Error != nil {
				logger.Errorf("[CRON] fail to auto cancel job %d: %v", reminder.JobId, result.Error)
				continue // 繼續處理其他工作，不回滾整個事務
			}

			if result.RowsAffected > 0 {
				// 收集成功取消的工作
				cancelledJobs = append(cancelledJobs, reminder)
			}
		}

		// 在同一事務中批量創建自動取消通知
		if len(cancelledJobs) > 0 {
			if err := services.SystemNotificationService.CreateFacilityJobAutoCancelBatch(tx, cancelledJobs); err != nil {
				logger.Errorf("[CRON] fail to send auto cancel notifications to facilities: %v", err)
				return err // 如果通知發送失敗，回滾整個事務
			}
		}

		return nil
	})

	if err != nil {
		logger.Errorf("[CRON] transaction failed for auto cancel jobs: %v", err)
	} else if len(cancelledJobs) > 0 {
		logger.Infof("Successfully auto cancelled %d jobs and sent notifications", len(cancelledJobs))
	}
}
